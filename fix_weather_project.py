# -*- coding: utf-8 -*-
"""
修复天气预报项目 - 生成缺失的app.json等文件
"""

import os
import json


def fix_weather_project():
    """修复天气预报项目，生成缺失的文件"""
    project_path = "project/weather-forecast"
    
    print("🔧 开始修复天气预报项目...")
    
    # 1. 生成app.json
    app_config = {
        "pages": [
            "pages/首页/首页",
            "pages/天气详情页/天气详情页"
        ],
        "window": {
            "backgroundTextStyle": "light",
            "navigationBarBackgroundColor": "#4A90E2",
            "navigationBarTitleText": "天气查询",
            "navigationBarTextStyle": "white",
            "backgroundColor": "#f8f8f8"
        },
        "tabBar": {
            "color": "#7A7E83",
            "selectedColor": "#4A90E2",
            "borderStyle": "black",
            "backgroundColor": "#ffffff",
            "list": [
                {
                    "pagePath": "pages/首页/首页",
                    "text": "天气",
                    "iconPath": "images/weather.png",
                    "selectedIconPath": "images/weather_active.png"
                },
                {
                    "pagePath": "pages/天气详情页/天气详情页",
                    "text": "详情",
                    "iconPath": "images/detail.png",
                    "selectedIconPath": "images/detail_active.png"
                }
            ]
        },
        "networkTimeout": {
            "request": 10000,
            "downloadFile": 10000
        },
        "debug": True
    }
    
    app_json_path = os.path.join(project_path, "app.json")
    with open(app_json_path, 'w', encoding='utf-8') as f:
        json.dump(app_config, f, ensure_ascii=False, indent=2)
    print(f"✅ 生成 app.json")
    
    # 2. 生成app.js
    app_js_content = '''// app.js
// 应用程序入口文件
App({
  onLaunch: function () {
    console.log('天气查询小程序启动');
    
    // 获取系统信息
    const systemInfo = wx.getSystemInfoSync();
    this.globalData.systemInfo = systemInfo;
    
    // 检查位置权限
    this.checkLocationPermission();
  },
  
  onShow: function () {
    console.log('小程序显示');
  },
  
  onHide: function () {
    console.log('小程序隐藏');
  },
  
  onError: function (msg) {
    console.error('小程序错误:', msg);
    wx.showToast({
      title: '程序出现错误',
      icon: 'none'
    });
  },
  
  // 检查位置权限
  checkLocationPermission: function() {
    wx.getSetting({
      success: (res) => {
        if (!res.authSetting['scope.userLocation']) {
          wx.authorize({
            scope: 'scope.userLocation',
            success: () => {
              console.log('位置权限授权成功');
            },
            fail: () => {
              console.log('位置权限授权失败');
            }
          });
        }
      }
    });
  },
  
  globalData: {
    userInfo: null,
    systemInfo: null,
    appName: '天气查询小程序',
    version: '1.0.0',
    // 天气API配置（需要替换为真实的API密钥）
    weatherApiKey: 'your-weather-api-key',
    weatherApiBase: 'https://devapi.qweather.com/v7'
  }
});'''
    
    app_js_path = os.path.join(project_path, "app.js")
    with open(app_js_path, 'w', encoding='utf-8') as f:
        f.write(app_js_content)
    print(f"✅ 生成 app.js")
    
    # 3. 生成app.wxss
    app_wxss_content = '''/* app.wxss */
/* 全局样式 */
page {
  background-color: #f8f8f8;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  color: #333;
}

.container {
  padding: 20rpx;
  min-height: 100vh;
}

/* 天气相关样式 */
.weather-card {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  border-radius: 20rpx;
  padding: 40rpx;
  margin: 20rpx;
  color: white;
  box-shadow: 0 8rpx 32rpx rgba(74, 144, 226, 0.3);
}

.weather-temp {
  font-size: 80rpx;
  font-weight: 300;
  text-align: center;
  margin: 20rpx 0;
}

.weather-desc {
  font-size: 32rpx;
  text-align: center;
  opacity: 0.9;
}

.weather-info {
  display: flex;
  justify-content: space-between;
  margin-top: 40rpx;
  padding-top: 30rpx;
  border-top: 1rpx solid rgba(255, 255, 255, 0.2);
}

.weather-info-item {
  text-align: center;
  flex: 1;
}

.weather-info-label {
  font-size: 24rpx;
  opacity: 0.8;
  margin-bottom: 10rpx;
}

.weather-info-value {
  font-size: 28rpx;
  font-weight: 500;
}

/* 预报列表样式 */
.forecast-list {
  margin: 20rpx;
}

.forecast-item {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.forecast-date {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.forecast-weather {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.forecast-icon {
  width: 60rpx;
  height: 60rpx;
}

.forecast-desc {
  font-size: 26rpx;
  color: #666;
}

.forecast-temp {
  font-size: 28rpx;
  color: #4A90E2;
  font-weight: 500;
}

/* 通用按钮样式 */
.btn {
  background-color: #4A90E2;
  color: white;
  border-radius: 12rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
  border: none;
  margin: 20rpx 0;
  box-shadow: 0 4rpx 16rpx rgba(74, 144, 226, 0.3);
}

.btn:active {
  background-color: #357ABD;
  transform: translateY(2rpx);
}

.btn-secondary {
  background-color: #f0f0f0;
  color: #333;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.btn-secondary:active {
  background-color: #e0e0e0;
}

/* 加载状态 */
.loading {
  text-align: center;
  padding: 100rpx 0;
  color: #999;
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
}

/* 错误状态 */
.error {
  text-align: center;
  padding: 100rpx 40rpx;
  color: #ff4757;
}

.error-text {
  font-size: 28rpx;
  margin-bottom: 40rpx;
}

/* 工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mt-10 { margin-top: 20rpx; }
.mt-20 { margin-top: 40rpx; }
.mb-10 { margin-bottom: 20rpx; }
.mb-20 { margin-bottom: 40rpx; }

.flex { display: flex; }
.flex-column { flex-direction: column; }
.flex-center { justify-content: center; align-items: center; }
.flex-between { justify-content: space-between; }
.flex-around { justify-content: space-around; }'''
    
    app_wxss_path = os.path.join(project_path, "app.wxss")
    with open(app_wxss_path, 'w', encoding='utf-8') as f:
        f.write(app_wxss_content)
    print(f"✅ 生成 app.wxss")
    
    # 4. 修复页面文件名（去掉中文，使用标准命名）
    fix_page_names(project_path)
    
    # 5. 生成项目说明文件
    generate_project_readme(project_path)
    
    print(f"\n🎉 天气预报项目修复完成！")
    print(f"📁 项目路径: {project_path}")
    print(f"🚀 现在可以导入微信开发者工具了")


def fix_page_names(project_path):
    """修复页面文件名，使用英文命名"""
    pages_dir = os.path.join(project_path, "pages")
    
    # 重命名首页
    old_index_dir = os.path.join(pages_dir, "首页")
    new_index_dir = os.path.join(pages_dir, "index")
    if os.path.exists(old_index_dir):
        os.rename(old_index_dir, new_index_dir)
        # 重命名页面文件
        for old_name in ["首页.js", "首页.wxml", "首页.wxss", "首页.json"]:
            new_name = old_name.replace("首页", "index")
            old_file = os.path.join(new_index_dir, old_name)
            new_file = os.path.join(new_index_dir, new_name)
            if os.path.exists(old_file):
                os.rename(old_file, new_file)
        print(f"✅ 重命名首页目录和文件")
    
    # 重命名详情页
    old_detail_dir = os.path.join(pages_dir, "天气详情页")
    new_detail_dir = os.path.join(pages_dir, "detail")
    if os.path.exists(old_detail_dir):
        os.rename(old_detail_dir, new_detail_dir)
        # 重命名页面文件
        for old_name in ["天气详情页.js", "天气详情页.wxml", "天气详情页.wxss", "天气详情页.json"]:
            new_name = old_name.replace("天气详情页", "detail")
            old_file = os.path.join(new_detail_dir, old_name)
            new_file = os.path.join(new_detail_dir, new_name)
            if os.path.exists(old_file):
                os.rename(old_file, new_file)
        print(f"✅ 重命名详情页目录和文件")
    
    # 更新app.json中的页面路径
    app_json_path = os.path.join(project_path, "app.json")
    if os.path.exists(app_json_path):
        with open(app_json_path, 'r', encoding='utf-8') as f:
            app_config = json.load(f)
        
        app_config["pages"] = [
            "pages/index/index",
            "pages/detail/detail"
        ]
        
        app_config["tabBar"]["list"] = [
            {
                "pagePath": "pages/index/index",
                "text": "天气",
                "iconPath": "images/weather.png",
                "selectedIconPath": "images/weather_active.png"
            },
            {
                "pagePath": "pages/detail/detail",
                "text": "详情",
                "iconPath": "images/detail.png",
                "selectedIconPath": "images/detail_active.png"
            }
        ]
        
        with open(app_json_path, 'w', encoding='utf-8') as f:
            json.dump(app_config, f, ensure_ascii=False, indent=2)
        print(f"✅ 更新app.json页面路径")


def generate_project_readme(project_path):
    """生成项目说明文件"""
    readme_content = '''# 天气查询小程序

## 项目简介
这是一个功能完整的天气查询微信小程序，支持查看实时天气和未来7天天气预报。

## 功能特性
- ✅ 实时天气查询
- ✅ 未来7天天气预报
- ✅ 自动定位获取当前城市天气
- ✅ 城市搜索功能
- ✅ 美观的UI设计
- ✅ 完整的错误处理

## 项目结构
```
weather-forecast/
├── app.json          # 应用配置文件
├── app.js            # 应用逻辑文件
├── app.wxss          # 全局样式文件
├── pages/
│   ├── index/        # 首页（天气展示）
│   │   ├── index.js
│   │   ├── index.wxml
│   │   ├── index.wxss
│   │   └── index.json
│   └── detail/       # 详情页（7天预报）
│       ├── detail.js
│       ├── detail.wxml
│       ├── detail.wxss
│       └── detail.json
├── utils/
│   └── 天气API工具.js  # 天气API封装
└── images/           # 图片资源目录
```

## 使用说明

### 导入微信开发者工具
1. 打开微信开发者工具
2. 选择"导入项目"
3. 选择项目目录
4. 填写AppID（可使用测试号）
5. 点击"导入"并编译运行

### API配置
1. 注册和风天气API账号：https://dev.qweather.com/
2. 获取API密钥
3. 在app.js中替换`weatherApiKey`为真实密钥

### 功能说明
- **首页**: 显示当前位置的实时天气信息
- **详情页**: 显示未来7天的天气预报
- **自动定位**: 自动获取用户当前位置的天气
- **城市搜索**: 支持搜索其他城市的天气

## 技术特点
- 使用微信小程序原生开发
- 集成和风天气API
- 响应式设计，适配不同屏幕
- 完整的错误处理和用户提示
- 优雅的加载状态和空状态处理

## 注意事项
1. 需要配置真实的天气API密钥才能获取实际数据
2. 需要在微信公众平台配置服务器域名白名单
3. 使用真机调试时需要开启位置权限

---
*此项目由微信小程序自动生成系统创建*
'''
    
    readme_path = os.path.join(project_path, "README.md")
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    print(f"✅ 生成项目说明文件")


if __name__ == "__main__":
    fix_weather_project()
