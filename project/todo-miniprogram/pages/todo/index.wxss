/* pages/todo/index.wxss */
.container {
  padding: 0;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.input-section {
  background-color: white;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.input-wrapper {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.todo-input {
  flex: 1;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #ddd;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.add-btn {
  background-color: #1aad19;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 0 30rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
}

.add-btn:active {
  background-color: #179b16;
}

.stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: white;
  border-bottom: 1rpx solid #eee;
}

.stats-text {
  font-size: 24rpx;
  color: #666;
}

.clear-btn {
  background-color: #ff4757;
  color: white;
  border: none;
  border-radius: 6rpx;
  padding: 10rpx 20rpx;
  font-size: 24rpx;
}

.todo-list {
  padding: 20rpx 0;
}

.todo-item {
  display: flex;
  align-items: center;
  background-color: white;
  margin: 0 30rpx 20rpx;
  padding: 30rpx;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.todo-item.completed {
  opacity: 0.6;
}

.todo-content {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.checkbox {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.checkbox.checked {
  background-color: #1aad19;
  border-color: #1aad19;
}

.checkmark {
  color: white;
  font-size: 24rpx;
  font-weight: bold;
}

.todo-text {
  flex: 1;
}

.text {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.completed .text {
  text-decoration: line-through;
  color: #999;
}

.time {
  font-size: 22rpx;
  color: #999;
}

.todo-actions {
  margin-left: 20rpx;
}

.delete-btn {
  background-color: #ff4757;
  color: white;
  border: none;
  border-radius: 6rpx;
  padding: 10rpx 20rpx;
  font-size: 24rpx;
}

.empty-state {
  text-align: center;
  padding: 100rpx 30rpx;
}

.empty-text {
  display: block;
  font-size: 32rpx;
  color: #999;
  margin-bottom: 20rpx;
}

.empty-tip {
  font-size: 24rpx;
  color: #ccc;
}