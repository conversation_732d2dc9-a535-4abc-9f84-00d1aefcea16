// pages/todo/index.js
const storage = require('../../utils/storage.js')

Page({
  data: {
    todos: [],
    inputValue: '',
    nextId: 1
  },

  onLoad() {
    this.loadTodos()
  },

  // 加载待办事项
  loadTodos() {
    const todos = storage.get('todos', [])
    const nextId = storage.get('nextId', 1)
    this.setData({
      todos,
      nextId
    })
  },

  // 保存待办事项
  saveTodos() {
    storage.set('todos', this.data.todos)
    storage.set('nextId', this.data.nextId)
  },

  // 输入框变化
  onInputChange(e) {
    this.setData({
      inputValue: e.detail.value
    })
  },

  // 添加待办事项
  addTodo() {
    const { inputValue, todos, nextId } = this.data
    if (!inputValue.trim()) {
      wx.showToast({
        title: '请输入待办事项',
        icon: 'none'
      })
      return
    }

    const newTodo = {
      id: nextId,
      text: inputValue.trim(),
      completed: false,
      createTime: new Date().toLocaleString()
    }

    this.setData({
      todos: [...todos, newTodo],
      inputValue: '',
      nextId: nextId + 1
    })

    this.saveTodos()
    
    wx.showToast({
      title: '添加成功',
      icon: 'success'
    })
  },

  // 切换完成状态
  toggleTodo(e) {
    const { id } = e.currentTarget.dataset
    const todos = this.data.todos.map(todo => {
      if (todo.id === id) {
        return { ...todo, completed: !todo.completed }
      }
      return todo
    })

    this.setData({ todos })
    this.saveTodos()
  },

  // 删除待办事项
  deleteTodo(e) {
    const { id } = e.currentTarget.dataset
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个待办事项吗？',
      success: (res) => {
        if (res.confirm) {
          const todos = this.data.todos.filter(todo => todo.id !== id)
          this.setData({ todos })
          this.saveTodos()
          
          wx.showToast({
            title: '删除成功',
            icon: 'success'
          })
        }
      }
    })
  },

  // 清空所有
  clearAll() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有待办事项吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            todos: [],
            nextId: 1
          })
          this.saveTodos()
          
          wx.showToast({
            title: '清空成功',
            icon: 'success'
          })
        }
      }
    })
  }
})