<!--pages/todo/index.wxml-->
<view class="container">
  <!-- 输入区域 -->
  <view class="input-section">
    <view class="input-wrapper">
      <input 
        class="todo-input" 
        placeholder="输入待办事项..." 
        value="{{inputValue}}"
        bindinput="onInputChange"
        confirm-type="done"
        bindconfirm="addTodo"
      />
      <button class="add-btn" bindtap="addTodo">添加</button>
    </view>
  </view>

  <!-- 统计信息 -->
  <view class="stats">
    <text class="stats-text">
      共 {{todos.length}} 项，已完成 {{todos.filter(item => item.completed).length}} 项
    </text>
    <button wx:if="{{todos.length > 0}}" class="clear-btn" bindtap="clearAll">清空</button>
  </view>

  <!-- 待办列表 -->
  <view class="todo-list">
    <view 
      wx:for="{{todos}}" 
      wx:key="id" 
      class="todo-item {{item.completed ? 'completed' : ''}}"
    >
      <view class="todo-content" bindtap="toggleTodo" data-id="{{item.id}}">
        <view class="checkbox {{item.completed ? 'checked' : ''}}">
          <text wx:if="{{item.completed}}" class="checkmark">✓</text>
        </view>
        <view class="todo-text">
          <text class="text">{{item.text}}</text>
          <text class="time">{{item.createTime}}</text>
        </view>
      </view>
      <view class="todo-actions">
        <button class="delete-btn" bindtap="deleteTodo" data-id="{{item.id}}">删除</button>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view wx:if="{{todos.length === 0}}" class="empty-state">
    <text class="empty-text">暂无待办事项</text>
    <text class="empty-tip">点击上方输入框添加新的待办事项</text>
  </view>
</view>