{"libVersion": "3.9.0", "projectname": "todo-miniprogram", "setting": {"urlCheck": true, "coverView": true, "lazyloadPlaceholderEnable": false, "skylineRenderEnable": false, "preloadBackgroundData": false, "autoAudits": false, "showShadowRootInWxmlPanel": true, "compileHotReLoad": true, "useApiHook": true, "useApiHostProcess": true, "useStaticServer": false, "useLanDebug": false, "showES6CompileOption": false, "checkInvalidKey": true, "ignoreDevUnusedFiles": true, "bigPackageSizeSupport": false}}