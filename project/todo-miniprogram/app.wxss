/**app.wxss**/
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 200rpx 0;
  box-sizing: border-box;
}

/* 通用样式 */
page {
  background-color: #f5f5f5;
}

.btn {
  background-color: #1aad19;
  color: white;
  border-radius: 8rpx;
  padding: 20rpx 40rpx;
  margin: 20rpx;
  font-size: 28rpx;
}

.btn:active {
  background-color: #179b16;
}

.card {
  background-color: white;
  margin: 20rpx;
  padding: 30rpx;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.text-center {
  text-align: center;
}

.mt-20 {
  margin-top: 40rpx;
}

.mb-20 {
  margin-bottom: 40rpx;
}