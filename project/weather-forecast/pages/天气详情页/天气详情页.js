// index.js
// 文件作用：页面逻辑代码，包括数据获取、处理和页面交互逻辑
Page({
  data: {
    city: '加载中...', // 当前城市
    weatherData: [], // 天气预报数据
    loading: true, // 加载状态
    error: null // 错误信息
  },

  onLoad() {
    this.getWeatherData();
  },

  // 获取天气数据
  getWeatherData() {
    this.setData({ loading: true, error: null });
    
    // 实际项目中这里应该是调用API接口
    // 这里使用模拟数据演示
    wx.request({
      url: 'https://mockapi.com/weather', // 模拟API地址
      method: 'GET',
      success: (res) => {
        if (res.statusCode === 200 && res.data) {
          this.processWeatherData(res.data);
        } else {
          this.setData({ 
            error: '获取天气数据失败，请稍后重试',
            loading: false 
          });
        }
      },
      fail: (err) => {
        console.error('请求失败:', err);
        this.setData({ 
          error: '网络请求失败，请检查网络连接',
          loading: false 
        });
        
        // 模拟数据，实际项目应删除
        this.processWeatherData(this.getMockData());
      }
    });
  },

  // 处理天气数据
  processWeatherData(data) {
    try {
      // 这里假设API返回的数据结构
      const forecast = data.forecast.slice(0, 7); // 只取7天数据
      this.setData({
        city: data.city || '未知城市',
        weatherData: forecast.map(item => ({
          date: item.date,
          week: item.week,
          dayWeather: item.dayWeather,
          nightWeather: item.nightWeather,
          dayTemp: item.dayTemp,
          nightTemp: item.nightTemp,
          wind: item.wind,
          icon: this.getWeatherIcon(item.dayWeather)
        })),
        loading: false
      });
    } catch (e) {
      console.error('数据处理错误:', e);
      this.setData({ 
        error: '数据处理出错，请刷新重试',
        loading: false 
      });
    }
  },

  // 根据天气类型获取图标
  getWeatherIcon(weather) {
    const icons = {
      '晴': 'sunny',
      '多云': 'cloudy',
      '阴': 'overcast',
      '雨': 'rainy',
      '雪': 'snowy',
      '雾': 'foggy'
    };
    return icons[weather] || 'unknown';
  },

  // 刷新数据
  onPullDownRefresh() {
    this.getWeatherData(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 模拟数据，实际项目应删除
  getMockData() {
    const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
    const weatherTypes = ['晴', '多云', '阴', '雨', '雪'];
    const mockData = {
      city: '北京市',
      forecast: []
    };
    
    const today = new Date();
    for (let i = 0; i < 7; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);
      
      mockData.forecast.push({
        date: `${date.getMonth()+1}月${date.getDate()}日`,
        week: weekDays[date.getDay()],
        dayWeather: weatherTypes[Math.floor(Math.random() * weatherTypes.length)],
        nightWeather: weatherTypes[Math.floor(Math.random() * weatherTypes.length)],
        dayTemp: Math.floor(15 + Math.random() * 15),
        nightTemp: Math.floor(5 + Math.random() * 10),
        wind: ['微风', '3-4级', '4-5级'][Math.floor(Math.random() * 3)]
      });
    }
    
    return mockData;
  }
});