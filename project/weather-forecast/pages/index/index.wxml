<!-- index.wxml -->
<!-- 文件作用：页面结构，展示天气信息和预报 -->
<view class="container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading">
    <text>加载中...</text>
    <image src="/images/loading.gif" mode="aspectFit"></image>
  </view>

  <!-- 错误提示 -->
  <view wx:if="{{error}}" class="error">
    <text>{{error}}</text>
    <button bindtap="onRefresh">重新加载</button>
  </view>

  <!-- 当前天气 -->
  <view wx:if="{{!loading && !error}}" class="current-weather">
    <view class="location">
      <text>{{currentWeather.city || '未知城市'}}</text>
    </view>
    <view class="weather-info">
      <image src="{{currentWeather.icon}}" mode="aspectFit"></image>
      <text class="temp">{{currentWeather.temp}}°C</text>
    </view>
    <view class="details">
      <text>湿度: {{currentWeather.humidity}}%</text>
      <text>风速: {{currentWeather.windSpeed}}km/h</text>
      <text>{{currentWeather.condition}}</text>
    </view>
  </view>

  <!-- 7天预报 -->
  <view wx:if="{{!loading && !error}}" class="forecast">
    <view class="title">
      <text>未来7天预报</text>
    </view>
    <scroll-view scroll-x class="forecast-list">
      <view wx:for="{{forecast}}" wx:key="date" class="forecast-item">
        <text>{{item.date}}</text>
        <image src="{{item.icon}}" mode="aspectFit"></image>
        <text>{{item.tempMin}}/{{item.tempMax}}°C</text>
      </view>
    </scroll-view>
  </view>

  <!-- 操作按钮 -->
  <view class="actions">
    <button bindtap="onRefresh" class="btn-primary">刷新数据</button>
    <button bindtap="goToDetail" class="btn-secondary">查看详情</button>
  </view>
</view>