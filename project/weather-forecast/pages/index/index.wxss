/* index.wxss */
/* 文件作用：页面样式，定义页面布局和外观 */
.container {
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: linear-gradient(to bottom, #4facfe 0%, #00f2fe 100%);
  color: #fff;
}

.loading, .error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300rpx;
}

.loading image {
  width: 80rpx;
  height: 80rpx;
  margin-top: 20rpx;
}

.error {
  color: #ff4d4f;
}

.error button {
  margin-top: 20rpx;
  background: #fff;
  color: #333;
}

.current-weather {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 40rpx 0;
}

.location {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.weather-info {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.weather-info image {
  width: 100rpx;
  height: 100rpx;
}

.temp {
  font-size: 72rpx;
  margin-left: 20rpx;
}

.details {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 28rpx;
  opacity: 0.8;
}

.forecast {
  margin-top: 40rpx;
}

.title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  text-align: center;
}

.forecast-list {
  white-space: nowrap;
  width: 100%;
}

.forecast-item {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 30rpx;
  margin-right: 20rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 16rpx;
}

.forecast-item image {
  width: 60rpx;
  height: 60rpx;
  margin: 10rpx 0;
}

.refresh {
  margin-top: 40rpx;
  display: flex;
  justify-content: center;
}

.refresh button {
  background: #fff;
  color: #333;
  width: 200rpx;
}