// index.js
// 文件作用：页面逻辑代码，包含数据获取、处理和页面交互逻辑
Page({
  data: {
    currentWeather: {},      // 当前天气数据
    forecast: [],            // 未来7天预报
    loading: true,          // 加载状态
    error: null             // 错误信息
  },

  onLoad() {
    this.getWeatherData();
  },

  // 获取天气数据
  getWeatherData() {
    this.setData({ loading: true, error: null });
    
    // 模拟API请求，实际开发中替换为真实天气API
    wx.request({
      url: 'https://mock.weather.api/current', // 替换为真实API地址
      method: 'GET',
      success: (res) => {
        if (res.data && res.data.success) {
          this.setData({
            currentWeather: res.data.data,
            loading: false
          });
          this.getForecastData();
        } else {
          this.handleError('获取天气数据失败');
        }
      },
      fail: (err) => {
        this.handleError('网络请求失败');
      }
    });
  },

  // 获取预报数据
  getForecastData() {
    wx.request({
      url: 'https://mock.weather.api/forecast', // 替换为真实API地址
      method: 'GET',
      success: (res) => {
        if (res.data && res.data.success) {
          this.setData({
            forecast: res.data.data.slice(0, 7), // 只取7天数据
            loading: false
          });
        } else {
          this.handleError('获取预报数据失败');
        }
      },
      fail: (err) => {
        this.handleError('网络请求失败');
      }
    });
  },

  // 错误处理
  handleError(msg) {
    this.setData({
      error: msg,
      loading: false
    });
    wx.showToast({
      title: msg,
      icon: 'none',
      duration: 2000
    });
  },

  // 刷新数据
  onRefresh() {
    this.getWeatherData();
  },

  // 跳转到详情页
  goToDetail() {
    wx.navigateTo({
      url: '/pages/detail/detail'
    });
  }
});