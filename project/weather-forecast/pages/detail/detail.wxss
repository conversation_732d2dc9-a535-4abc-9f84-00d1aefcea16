/* index.wxss */
/* 文件作用：页面样式，美化天气预报界面 */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 头部样式 */
.header {
  text-align: center;
  margin-bottom: 30rpx;
  padding: 20rpx 0;
  background-color: #4a8cff;
  color: white;
  border-radius: 10rpx;
}

.city {
  font-size: 40rpx;
  font-weight: bold;
}

.subtitle {
  font-size: 28rpx;
  opacity: 0.9;
}

/* 加载状态 */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading image {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 20rpx;
}

/* 错误提示 */
.error {
  text-align: center;
  padding: 40rpx;
  color: #ff4d4f;
}

.error button {
  margin-top: 20rpx;
}

/* 天气预报列表 */
.weather-list {
  background-color: white;
  border-radius: 10rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.weather-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.weather-item:last-child {
  border-bottom: none;
}

.date-info {
  width: 180rpx;
}

.date {
  font-size: 30rpx;
  font-weight: bold;
  display: block;
}

.week {
  font-size: 24rpx;
  color: #888;
}

.weather-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 150rpx;
}

.weather-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 10rpx;
}

.temp-range {
  text-align: center;
}

.day-temp {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.night-temp {
  font-size: 28rpx;
  color: #666;
}

.weather-detail {
  flex: 1;
  margin-left: 30rpx;
}

.day-weather, .night-weather, .wind {
  display: block;
  font-size: 26rpx;
  color: #555;
  line-height: 1.6;
}