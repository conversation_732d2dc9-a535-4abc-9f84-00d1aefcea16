<!-- index.wxml -->
<!-- 文件作用：页面结构，展示天气预报信息 -->
<view class="container">
  <!-- 头部城市信息 -->
  <view class="header">
    <view class="city">{{city}}</view>
    <view class="subtitle">未来7天天气预报</view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading">
    <image src="/images/loading.gif" mode="aspectFit"></image>
    <text>加载中...</text>
  </view>

  <!-- 错误提示 -->
  <view wx:if="{{error}}" class="error">
    <text>{{error}}</text>
    <button bindtap="getWeatherData" size="mini">重新加载</button>
  </view>

  <!-- 天气预报列表 -->
  <view wx:if="{{!loading && !error}}" class="weather-list">
    <view wx:for="{{weatherData}}" wx:key="date" class="weather-item">
      <view class="date-info">
        <text class="date">{{item.date}}</text>
        <text class="week">{{item.week}}</text>
      </view>
      
      <view class="weather-info">
        <image class="weather-icon" src="/images/{{item.icon}}.png"></image>
        <view class="temp-range">
          <text class="day-temp">{{item.dayTemp}}°C</text>
          <text class="night-temp">/{{item.nightTemp}}°C</text>
        </view>
      </view>
      
      <view class="weather-detail">
        <text class="day-weather">白天: {{item.dayWeather}}</text>
        <text class="night-weather">夜间: {{item.nightWeather}}</text>
        <text class="wind">风向: {{item.wind}}</text>
      </view>
    </view>
  </view>
</view>