// app.js
// 应用程序入口文件
App({
  onLaunch: function () {
    console.log('天气查询小程序启动');
    
    // 获取系统信息
    const systemInfo = wx.getSystemInfoSync();
    this.globalData.systemInfo = systemInfo;
    
    // 检查位置权限
    this.checkLocationPermission();
  },
  
  onShow: function () {
    console.log('小程序显示');
  },
  
  onHide: function () {
    console.log('小程序隐藏');
  },
  
  onError: function (msg) {
    console.error('小程序错误:', msg);
    wx.showToast({
      title: '程序出现错误',
      icon: 'none'
    });
  },
  
  // 检查位置权限
  checkLocationPermission: function() {
    wx.getSetting({
      success: (res) => {
        if (!res.authSetting['scope.userLocation']) {
          wx.authorize({
            scope: 'scope.userLocation',
            success: () => {
              console.log('位置权限授权成功');
            },
            fail: () => {
              console.log('位置权限授权失败');
            }
          });
        }
      }
    });
  },
  
  globalData: {
    userInfo: null,
    systemInfo: null,
    appName: '天气查询小程序',
    version: '1.0.0',
    // 天气API配置（需要替换为真实的API密钥）
    weatherApiKey: 'your-weather-api-key',
    weatherApiBase: 'https://devapi.qweather.com/v7'
  }
});