/* app.wxss */
/* 全局样式 */
page {
  background-color: #f8f8f8;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  color: #333;
}

.container {
  padding: 20rpx;
  min-height: 100vh;
}

/* 天气相关样式 */
.weather-card {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  border-radius: 20rpx;
  padding: 40rpx;
  margin: 20rpx;
  color: white;
  box-shadow: 0 8rpx 32rpx rgba(74, 144, 226, 0.3);
}

.weather-temp {
  font-size: 80rpx;
  font-weight: 300;
  text-align: center;
  margin: 20rpx 0;
}

.weather-desc {
  font-size: 32rpx;
  text-align: center;
  opacity: 0.9;
}

.weather-info {
  display: flex;
  justify-content: space-between;
  margin-top: 40rpx;
  padding-top: 30rpx;
  border-top: 1rpx solid rgba(255, 255, 255, 0.2);
}

.weather-info-item {
  text-align: center;
  flex: 1;
}

.weather-info-label {
  font-size: 24rpx;
  opacity: 0.8;
  margin-bottom: 10rpx;
}

.weather-info-value {
  font-size: 28rpx;
  font-weight: 500;
}

/* 预报列表样式 */
.forecast-list {
  margin: 20rpx;
}

.forecast-item {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.forecast-date {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.forecast-weather {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.forecast-icon {
  width: 60rpx;
  height: 60rpx;
}

.forecast-desc {
  font-size: 26rpx;
  color: #666;
}

.forecast-temp {
  font-size: 28rpx;
  color: #4A90E2;
  font-weight: 500;
}

/* 通用按钮样式 */
.btn {
  background-color: #4A90E2;
  color: white;
  border-radius: 12rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
  border: none;
  margin: 20rpx 0;
  box-shadow: 0 4rpx 16rpx rgba(74, 144, 226, 0.3);
}

.btn:active {
  background-color: #357ABD;
  transform: translateY(2rpx);
}

.btn-secondary {
  background-color: #f0f0f0;
  color: #333;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.btn-secondary:active {
  background-color: #e0e0e0;
}

/* 加载状态 */
.loading {
  text-align: center;
  padding: 100rpx 0;
  color: #999;
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
}

/* 错误状态 */
.error {
  text-align: center;
  padding: 100rpx 40rpx;
  color: #ff4757;
}

.error-text {
  font-size: 28rpx;
  margin-bottom: 40rpx;
}

/* 工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mt-10 { margin-top: 20rpx; }
.mt-20 { margin-top: 40rpx; }
.mb-10 { margin-bottom: 20rpx; }
.mb-20 { margin-bottom: 40rpx; }

.flex { display: flex; }
.flex-column { flex-direction: column; }
.flex-center { justify-content: center; align-items: center; }
.flex-between { justify-content: space-between; }
.flex-around { justify-content: space-around; }