# 天气查询小程序

## 项目简介
这是一个功能完整的天气查询微信小程序，支持查看实时天气和未来7天天气预报。

## 功能特性
- ✅ 实时天气查询
- ✅ 未来7天天气预报
- ✅ 自动定位获取当前城市天气
- ✅ 城市搜索功能
- ✅ 美观的UI设计
- ✅ 完整的错误处理

## 项目结构
```
weather-forecast/
├── app.json          # 应用配置文件
├── app.js            # 应用逻辑文件
├── app.wxss          # 全局样式文件
├── pages/
│   ├── index/        # 首页（天气展示）
│   │   ├── index.js
│   │   ├── index.wxml
│   │   ├── index.wxss
│   │   └── index.json
│   └── detail/       # 详情页（7天预报）
│       ├── detail.js
│       ├── detail.wxml
│       ├── detail.wxss
│       └── detail.json
├── utils/
│   └── 天气API工具.js  # 天气API封装
└── images/           # 图片资源目录
```

## 使用说明

### 导入微信开发者工具
1. 打开微信开发者工具
2. 选择"导入项目"
3. 选择项目目录
4. 填写AppID（可使用测试号）
5. 点击"导入"并编译运行

### API配置
1. 注册和风天气API账号：https://dev.qweather.com/
2. 获取API密钥
3. 在app.js中替换`weatherApiKey`为真实密钥

### 功能说明
- **首页**: 显示当前位置的实时天气信息
- **详情页**: 显示未来7天的天气预报
- **自动定位**: 自动获取用户当前位置的天气
- **城市搜索**: 支持搜索其他城市的天气

## 技术特点
- 使用微信小程序原生开发
- 集成和风天气API
- 响应式设计，适配不同屏幕
- 完整的错误处理和用户提示
- 优雅的加载状态和空状态处理

## 注意事项
1. 需要配置真实的天气API密钥才能获取实际数据
2. 需要在微信公众平台配置服务器域名白名单
3. 使用真机调试时需要开启位置权限

---
*此项目由微信小程序自动生成系统创建*
