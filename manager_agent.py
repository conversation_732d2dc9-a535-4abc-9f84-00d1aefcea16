# -*- coding: utf-8 -*-
"""
Manager Agent - 任务协调者
使用qwen大模型进行任务分解和调度
"""

import json
import re
import time
from typing import Dict, List, Any
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from config import API_CONFIGS, PROJECT_CONFIG


class ManagerAgent:
    """任务协调者Agent"""
    
    def __init__(self):
        self.config = API_CONFIGS["qwen"]
        self.project_config = PROJECT_CONFIG
        self.session = self._create_session()

    def _create_session(self):
        """创建带有重试机制的会话"""
        session = requests.Session()

        retry_strategy = Retry(
            total=PROJECT_CONFIG["max_retries"],
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["POST"]
        )

        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)

        return session
        
    def _call_qwen_api(self, prompt: str) -> str:
        """调用qwen API，带重试机制"""
        headers = {
            "Authorization": f"Bearer {self.config['api_key']}",
            "Content-Type": "application/json"
        }

        data = {
            "model": self.config["model"],
            "messages": [
                {"role": "system", "content": "你是一个专业的微信小程序项目管理专家，负责分析需求、分解任务、协调开发流程。"},
                {"role": "user", "content": prompt}
            ],
            "temperature": 0.7,
            "max_tokens": 2000
        }

        for attempt in range(self.project_config["max_retries"]):
            try:
                response = self.session.post(
                    f"{self.config['base_url']}/chat/completions",
                    headers=headers,
                    json=data,
                    timeout=(self.project_config["connect_timeout"], self.project_config["read_timeout"])
                )
                response.raise_for_status()
                result = response.json()
                return result["choices"][0]["message"]["content"]

            except requests.exceptions.Timeout as e:
                print(f"   ⚠️  Qwen API超时 (尝试 {attempt + 1}/{self.project_config['max_retries']})")
                if attempt < self.project_config["max_retries"] - 1:
                    time.sleep(self.project_config["retry_delay"])
                    continue

            except Exception as e:
                print(f"   ⚠️  Qwen API调用失败 (尝试 {attempt + 1}/{self.project_config['max_retries']}): {e}")
                if attempt < self.project_config["max_retries"] - 1:
                    time.sleep(self.project_config["retry_delay"])
                    continue

        print(f"   ❌ Qwen API调用失败，所有重试已用完")
        return ""
    
    def analyze_requirements(self, user_input: str) -> Dict[str, Any]:
        """分析用户需求"""
        prompt = f"""
        请分析以下微信小程序开发需求，并输出结构化的分析结果：

        用户需求：{user_input}

        请按以下JSON格式输出分析结果：
        {{
            "project_name": "项目名称",
            "description": "项目描述",
            "pages": [
                {{
                    "name": "页面名称",
                    "path": "页面路径",
                    "description": "页面功能描述",
                    "components": ["需要的组件列表"]
                }}
            ],
            "utils": [
                {{
                    "name": "工具名称",
                    "description": "工具功能描述"
                }}
            ],
            "features": ["功能特性列表"],
            "complexity": "简单/中等/复杂"
        }}
        """
        
        response = self._call_qwen_api(prompt)
        
        try:
            # 提取JSON部分
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                return json.loads(json_match.group())
        except json.JSONDecodeError:
            pass
        
        # 如果解析失败，返回默认结构
        return {
            "project_name": "微信小程序",
            "description": user_input,
            "pages": [{"name": "index", "path": "pages/index/index", "description": user_input, "components": []}],
            "utils": [{"name": "storage", "description": "本地存储工具"}],
            "features": [user_input],
            "complexity": "中等"
        }
    
    def create_task_plan(self, requirements: Dict[str, Any]) -> List[Dict[str, Any]]:
        """创建任务计划"""
        prompt = f"""
        基于以下需求分析结果，创建详细的开发任务计划：

        需求分析：{json.dumps(requirements, ensure_ascii=False, indent=2)}

        请按以下JSON格式输出任务计划：
        {{
            "tasks": [
                {{
                    "id": "任务ID",
                    "type": "page/util/config",
                    "name": "任务名称",
                    "description": "任务描述",
                    "dependencies": ["依赖的任务ID列表"],
                    "priority": "高/中/低",
                    "estimated_time": "预估时间（分钟）"
                }}
            ],
            "execution_order": ["按执行顺序排列的任务ID"]
        }}
        """
        
        response = self._call_qwen_api(prompt)
        
        try:
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                plan = json.loads(json_match.group())
                return plan.get("tasks", [])
        except json.JSONDecodeError:
            pass
        
        # 默认任务计划
        tasks = []
        task_id = 1
        
        # 创建页面任务
        for page in requirements.get("pages", []):
            tasks.append({
                "id": f"page_{task_id}",
                "type": "page",
                "name": f"创建{page['name']}页面",
                "description": page["description"],
                "dependencies": [],
                "priority": "高",
                "estimated_time": "30"
            })
            task_id += 1
        
        # 创建工具任务
        for util in requirements.get("utils", []):
            tasks.append({
                "id": f"util_{task_id}",
                "type": "util",
                "name": f"创建{util['name']}工具",
                "description": util["description"],
                "dependencies": [],
                "priority": "中",
                "estimated_time": "20"
            })
            task_id += 1
        
        # 创建配置任务
        tasks.append({
            "id": f"config_{task_id}",
            "type": "config",
            "name": "创建应用配置",
            "description": "生成app.json等配置文件",
            "dependencies": [task["id"] for task in tasks],
            "priority": "高",
            "estimated_time": "15"
        })
        
        return tasks
    
    def validate_project(self, project_path: str) -> Dict[str, Any]:
        """验证项目完整性"""
        prompt = f"""
        请验证微信小程序项目的完整性和正确性。

        项目路径：{project_path}

        请检查以下方面：
        1. 必要文件是否存在（app.json, app.js, app.wxss）
        2. 页面文件是否完整（.js, .wxml, .wxss, .json）
        3. 配置文件格式是否正确
        4. 代码语法是否有明显错误

        请以JSON格式输出验证结果：
        {{
            "is_valid": true/false,
            "missing_files": ["缺失的文件列表"],
            "errors": ["错误列表"],
            "warnings": ["警告列表"],
            "suggestions": ["改进建议"]
        }}
        """
        
        response = self._call_qwen_api(prompt)
        
        try:
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                return json.loads(json_match.group())
        except json.JSONDecodeError:
            pass
        
        return {
            "is_valid": True,
            "missing_files": [],
            "errors": [],
            "warnings": [],
            "suggestions": []
        }
