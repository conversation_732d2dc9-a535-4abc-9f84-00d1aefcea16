# -*- coding: utf-8 -*-
"""
演示程序 - 生成示例微信小程序项目
"""

import os
from main import MiniProgramGenerator


def create_demo_project():
    """创建演示项目"""
    generator = MiniProgramGenerator()
    
    # 示例需求：待办事项管理小程序
    user_input = """
    我想要一个待办事项管理的微信小程序，功能包括：
    1. 首页显示待办事项列表
    2. 可以添加新的待办事项
    3. 可以标记待办事项为完成
    4. 可以删除待办事项
    5. 数据保存在本地存储中
    """
    
    print("🎯 正在生成待办事项管理小程序演示项目...")
    result = generator.generate_project(user_input, "todo-demo")
    
    if result["success"]:
        print("\n✨ 演示项目生成成功!")
        print(f"📁 项目路径: {result['project_path']}")
        
        # 显示生成的文件
        print("\n📋 生成的文件:")
        for file in result["file_list"]:
            print(f"   - {file}")
        
        # 创建README文件
        readme_content = f"""# {result['requirements']['project_name']}

## 项目描述
{result['requirements']['description']}

## 功能特性
{chr(10).join(f"- {feature}" for feature in result['requirements']['features'])}

## 项目结构
```
{chr(10).join(result['file_list'])}
```

## 使用说明
1. 使用微信开发者工具打开项目文件夹
2. 点击"编译"按钮进行编译
3. 在模拟器中预览效果

## 页面说明
{chr(10).join(f"- **{page['name']}**: {page['description']}" for page in result['requirements']['pages'])}

## 工具说明
{chr(10).join(f"- **{util['name']}**: {util['description']}" for util in result['requirements']['utils'])}

---
*此项目由微信小程序自动生成系统创建*
"""
        
        readme_path = os.path.join(result['project_path'], "README.md")
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        print(f"\n📖 已生成项目说明文件: README.md")
        print(f"\n🚀 可以直接用微信开发者工具打开项目文件夹进行开发!")
        
    else:
        print(f"\n❌ 演示项目生成失败: {result.get('error', '未知错误')}")


def create_weather_demo():
    """创建天气小程序演示"""
    generator = MiniProgramGenerator()
    
    user_input = """
    用户打开页面时可以看到当前城市的实时天气（温度、天气类型），以及未来7天的天气预报。
    """
    
    print("🌤️  正在生成天气预报小程序演示项目...")
    result = generator.generate_project(user_input, "weather-demo")
    
    if result["success"]:
        print(f"\n✨ 天气预报小程序生成成功!")
        print(f"📁 项目路径: {result['project_path']}")
    else:
        print(f"\n❌ 天气预报小程序生成失败: {result.get('error', '未知错误')}")


if __name__ == "__main__":
    print("🎯 微信小程序自动生成系统 - 演示程序")
    print("=" * 60)
    
    print("\n请选择要生成的演示项目:")
    print("1. 待办事项管理小程序")
    print("2. 天气预报小程序")
    print("3. 生成两个演示项目")
    
    choice = input("\n请输入选择 (1-3): ").strip()
    
    if choice == "1":
        create_demo_project()
    elif choice == "2":
        create_weather_demo()
    elif choice == "3":
        create_demo_project()
        print("\n" + "-" * 60)
        create_weather_demo()
    else:
        print("❌ 无效选择")
