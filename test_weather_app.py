# -*- coding: utf-8 -*-
"""
测试天气预报小程序生成
"""

import os
import sys
from main import MiniProgramGenerator


def test_weather_forecast():
    """测试生成天气预报小程序"""
    print("🌤️  开始测试天气预报小程序生成")
    print("=" * 60)
    
    # 创建生成器实例
    generator = MiniProgramGenerator()
    
    # 定义需求
    user_input = "我想要一个天气查询的小程序，可以展示实时天气和未来7天天气预报"
    project_name = "weather-forecast"
    
    print(f"📝 用户需求: {user_input}")
    print(f"📁 项目名称: {project_name}")
    print()
    
    try:
        # 生成项目
        result = generator.generate_project(user_input, project_name)
        
        if result["success"]:
            print("\n🎉 天气预报小程序生成成功!")
            print(f"📁 项目路径: {result['project_path']}")
            print(f"📄 生成文件数: {result['files_generated']} 个")
            
            print("\n📋 需求分析结果:")
            requirements = result["requirements"]
            print(f"   项目名称: {requirements.get('project_name', 'N/A')}")
            print(f"   项目描述: {requirements.get('description', 'N/A')}")
            print(f"   复杂度: {requirements.get('complexity', 'N/A')}")
            
            print("\n📄 生成的页面:")
            for page in requirements.get("pages", []):
                print(f"   - {page.get('name', 'N/A')}: {page.get('description', 'N/A')}")
            
            print("\n🔧 生成的工具:")
            for util in requirements.get("utils", []):
                print(f"   - {util.get('name', 'N/A')}: {util.get('description', 'N/A')}")
            
            print("\n📁 生成的文件列表:")
            for file in result["file_list"]:
                print(f"   - {file}")
            
            print("\n✅ 项目验证结果:")
            validation = result["validation"]
            if validation.get("is_valid", True):
                print("   项目验证通过 ✅")
            else:
                print("   项目验证发现问题 ⚠️")
                for error in validation.get("errors", []):
                    print(f"     - {error}")
            
            print(f"\n🚀 使用微信开发者工具导入步骤:")
            print(f"   1. 打开微信开发者工具")
            print(f"   2. 选择'导入项目'")
            print(f"   3. 选择项目目录: {result['project_path']}")
            print(f"   4. 填写AppID（可使用测试号）")
            print(f"   5. 点击'导入'并编译运行")
            
            return True
            
        else:
            print(f"\n❌ 天气预报小程序生成失败!")
            print(f"错误信息: {result.get('error', '未知错误')}")
            return False
            
    except Exception as e:
        print(f"\n💥 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return False


def check_api_config():
    """检查API配置"""
    print("🔍 检查API配置...")
    
    from config import API_CONFIGS
    
    for model_name, config in API_CONFIGS.items():
        api_key = config.get("api_key", "")
        if api_key and api_key != f"your-{model_name}-api-key":
            print(f"   ✅ {model_name.upper()} API密钥已配置")
        else:
            print(f"   ❌ {model_name.upper()} API密钥未配置")
    
    print()


if __name__ == "__main__":
    print("🧪 微信小程序自动生成系统 - API测试")
    print("=" * 60)
    
    # 检查API配置
    check_api_config()
    
    # 测试生成天气预报小程序
    success = test_weather_forecast()
    
    if success:
        print("\n🎊 测试完成！天气预报小程序生成成功！")
    else:
        print("\n😞 测试失败，请检查API配置和网络连接")
