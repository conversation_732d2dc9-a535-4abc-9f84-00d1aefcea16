# -*- coding: utf-8 -*-
"""
自动演示程序 - 无需交互直接生成示例项目
"""

from simple_generator import SimpleMiniProgramGenerator


def main():
    """自动生成演示项目"""
    print("🎯 微信小程序自动生成系统 - 自动演示")
    print("=" * 60)
    
    generator = SimpleMiniProgramGenerator()
    
    # 自动生成待办事项管理小程序
    description = "待办事项管理小程序，可以添加、删除、标记完成待办事项，数据保存在本地存储中"
    project_name = "todo-miniprogram"
    
    print(f"📝 需求描述: {description}")
    print(f"📁 项目名称: {project_name}")
    print()
    
    success = generator.generate_project(description, project_name)
    
    if success:
        print(f"\n🎉 项目生成成功!")
        print(f"📁 项目路径: project/{project_name}")
        print(f"🚀 可以直接用微信开发者工具打开项目文件夹!")
        
        print(f"\n📋 生成的文件包括:")
        print(f"   ✅ app.json - 应用配置文件")
        print(f"   ✅ app.js - 应用逻辑文件") 
        print(f"   ✅ app.wxss - 全局样式文件")
        print(f"   ✅ pages/index/ - 首页（用户信息展示）")
        print(f"   ✅ pages/todo/ - 待办页面（核心功能）")
        print(f"   ✅ utils/storage.js - 本地存储工具")
        
        print(f"\n🔧 核心功能:")
        print(f"   ✅ 添加待办事项")
        print(f"   ✅ 标记完成状态")
        print(f"   ✅ 删除待办事项")
        print(f"   ✅ 清空所有待办")
        print(f"   ✅ 本地数据持久化")
        print(f"   ✅ 统计信息显示")
        
        print(f"\n📱 使用微信开发者工具导入步骤:")
        print(f"   1. 打开微信开发者工具")
        print(f"   2. 选择'导入项目'")
        print(f"   3. 选择项目目录: project/{project_name}")
        print(f"   4. 填写AppID（可使用测试号）")
        print(f"   5. 点击'导入'并编译运行")
        
    else:
        print(f"\n❌ 项目生成失败")


if __name__ == "__main__":
    main()
