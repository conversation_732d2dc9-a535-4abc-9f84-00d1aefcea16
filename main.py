# -*- coding: utf-8 -*-
"""
微信小程序自动生成系统 - 主程序
"""

import os
import json
import time
from typing import Dict, Any

from manager_agent import ManagerAgent
from code_writer_agent import CodeWriterAgent
from file_manager_agent import FileManagerAgent
from config import PROJECT_CONFIG


class MiniProgramGenerator:
    """微信小程序生成器"""
    
    def __init__(self):
        self.manager = ManagerAgent()
        self.code_writer = CodeWriterAgent()
        self.file_manager = FileManagerAgent()
        
    def generate_project(self, user_input: str, project_name: str = None) -> Dict[str, Any]:
        """生成微信小程序项目"""
        print("=" * 60)
        print("🚀 微信小程序自动生成系统启动")
        print("=" * 60)
        
        try:
            # 1. 需求分析
            print("\n📋 步骤1: 分析用户需求...")
            requirements = self.manager.analyze_requirements(user_input)
            
            if not project_name:
                project_name = requirements.get("project_name", "miniprogram").replace(" ", "_")
            
            print(f"✅ 需求分析完成")
            print(f"   项目名称: {requirements.get('project_name', '未知')}")
            print(f"   页面数量: {len(requirements.get('pages', []))}")
            print(f"   工具数量: {len(requirements.get('utils', []))}")
            
            # 2. 创建任务计划
            print("\n📝 步骤2: 创建任务计划...")
            tasks = self.manager.create_task_plan(requirements)
            print(f"✅ 任务计划创建完成，共{len(tasks)}个任务")
            
            # 3. 创建项目结构
            print("\n📁 步骤3: 创建项目结构...")
            if not self.file_manager.create_project_structure(project_name, requirements):
                raise Exception("创建项目结构失败")
            print("✅ 项目结构创建完成")
            
            # 4. 生成页面代码
            print("\n💻 步骤4: 生成页面代码...")
            for page in requirements.get("pages", []):
                page_name = page.get("name", "index")
                description = page.get("description", "页面")
                
                print(f"   正在生成 {page_name} 页面...")
                code_files = self.code_writer.generate_page_code(page_name, description)
                
                if not self.file_manager.save_page_files(project_name, page_name, code_files):
                    print(f"   ⚠️  保存 {page_name} 页面失败")
                else:
                    print(f"   ✅ {page_name} 页面生成完成")
            
            # 5. 生成工具代码
            print("\n🔧 步骤5: 生成工具代码...")
            for util in requirements.get("utils", []):
                util_name = util.get("name", "util")
                description = util.get("description", "工具")
                
                print(f"   正在生成 {util_name} 工具...")
                util_code = self.code_writer.generate_util_code(util_name, description)
                
                if not self.file_manager.save_util_file(project_name, util_name, util_code):
                    print(f"   ⚠️  保存 {util_name} 工具失败")
                else:
                    print(f"   ✅ {util_name} 工具生成完成")
            
            # 6. 生成应用配置
            print("\n⚙️  步骤6: 生成应用配置...")
            pages_list = [f"pages/{page.get('name', 'index')}/index" for page in requirements.get("pages", [])]
            app_config = self.code_writer.generate_app_config(pages_list, requirements.get("description", ""))
            
            if not self.file_manager.save_app_files(project_name, app_config, requirements):
                print("   ⚠️  保存应用配置失败")
            else:
                print("   ✅ 应用配置生成完成")
            
            # 7. 项目验证
            print("\n🔍 步骤7: 验证项目完整性...")
            project_path = os.path.join(PROJECT_CONFIG["output_dir"], project_name)
            validation_result = self.manager.validate_project(project_path)
            
            if validation_result.get("is_valid", True):
                print("✅ 项目验证通过")
            else:
                print("⚠️  项目验证发现问题:")
                for error in validation_result.get("errors", []):
                    print(f"   - {error}")
            
            # 8. 生成项目报告
            project_files = self.file_manager.get_project_files(project_name)
            
            result = {
                "success": True,
                "project_name": project_name,
                "project_path": project_path,
                "requirements": requirements,
                "tasks_completed": len(tasks),
                "files_generated": len(project_files),
                "file_list": project_files,
                "validation": validation_result
            }
            
            print("\n" + "=" * 60)
            print("🎉 项目生成完成!")
            print("=" * 60)
            print(f"📁 项目路径: {project_path}")
            print(f"📄 生成文件: {len(project_files)} 个")
            print(f"💡 可直接导入微信开发者工具运行")
            print("=" * 60)
            
            return result
            
        except Exception as e:
            print(f"\n❌ 生成项目失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "project_name": project_name
            }
    
    def list_projects(self) -> list:
        """列出所有项目"""
        try:
            output_dir = PROJECT_CONFIG["output_dir"]
            if not os.path.exists(output_dir):
                return []
            
            projects = []
            for item in os.listdir(output_dir):
                item_path = os.path.join(output_dir, item)
                if os.path.isdir(item_path):
                    # 检查是否是微信小程序项目
                    app_json_path = os.path.join(item_path, "app.json")
                    if os.path.exists(app_json_path):
                        projects.append({
                            "name": item,
                            "path": item_path,
                            "created_time": os.path.getctime(item_path)
                        })
            
            return sorted(projects, key=lambda x: x["created_time"], reverse=True)
            
        except Exception as e:
            print(f"获取项目列表失败: {e}")
            return []


def main():
    """主函数"""
    generator = MiniProgramGenerator()
    
    print("🎯 微信小程序自动生成系统")
    print("输入您的需求，系统将自动生成完整的微信小程序项目")
    print("-" * 60)
    
    while True:
        print("\n请选择操作:")
        print("1. 生成新项目")
        print("2. 查看已有项目")
        print("3. 退出")
        
        choice = input("\n请输入选择 (1-3): ").strip()
        
        if choice == "1":
            user_input = input("\n请描述您想要的微信小程序功能: ").strip()
            if user_input:
                project_name = input("请输入项目名称 (留空自动生成): ").strip() or None
                result = generator.generate_project(user_input, project_name)
                
                if result["success"]:
                    print(f"\n✨ 项目生成成功!")
                    print(f"📁 项目位置: {result['project_path']}")
                    print(f"📋 生成文件列表:")
                    for file in result["file_list"]:
                        print(f"   - {file}")
                else:
                    print(f"\n❌ 项目生成失败: {result.get('error', '未知错误')}")
            else:
                print("❌ 请输入有效的需求描述")
        
        elif choice == "2":
            projects = generator.list_projects()
            if projects:
                print(f"\n📁 已有项目 ({len(projects)}个):")
                for i, project in enumerate(projects, 1):
                    print(f"   {i}. {project['name']} - {project['path']}")
            else:
                print("\n📭 暂无项目")
        
        elif choice == "3":
            print("\n👋 再见!")
            break
        
        else:
            print("❌ 无效选择，请重新输入")


if __name__ == "__main__":
    main()
