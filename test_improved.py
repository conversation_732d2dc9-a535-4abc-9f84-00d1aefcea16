# -*- coding: utf-8 -*-
"""
改进版测试脚本 - 测试天气预报小程序生成
"""

import os
import sys
from main import MiniProgramGenerator


def test_weather_forecast_improved():
    """测试生成天气预报小程序（改进版）"""
    print("🌤️  开始测试改进版天气预报小程序生成")
    print("=" * 60)
    
    # 创建生成器实例
    generator = MiniProgramGenerator()
    
    # 定义需求
    user_input = "我想要一个天气查询的小程序，可以展示实时天气和未来7天天气预报"
    project_name = "weather-forecast"
    
    print(f"📝 用户需求: {user_input}")
    print(f"📁 项目名称: {project_name}")
    print(f"🔧 改进内容:")
    print(f"   - 增加API重试机制（最多3次）")
    print(f"   - 增加超时时间（连接30秒，读取90秒）")
    print(f"   - 添加连接池和会话复用")
    print(f"   - 改进错误处理和日志输出")
    print()
    
    try:
        # 生成项目
        result = generator.generate_project(user_input, project_name)
        
        if result["success"]:
            print("\n🎉 天气预报小程序生成成功!")
            print(f"📁 项目路径: {result['project_path']}")
            print(f"📄 生成文件数: {result['files_generated']} 个")
            
            print("\n📋 需求分析结果:")
            requirements = result["requirements"]
            print(f"   项目名称: {requirements.get('project_name', 'N/A')}")
            print(f"   项目描述: {requirements.get('description', 'N/A')}")
            print(f"   复杂度: {requirements.get('complexity', 'N/A')}")
            
            print("\n📄 生成的页面:")
            for page in requirements.get("pages", []):
                print(f"   - {page.get('name', 'N/A')}: {page.get('description', 'N/A')}")
            
            print("\n🔧 生成的工具:")
            for util in requirements.get("utils", []):
                print(f"   - {util.get('name', 'N/A')}: {util.get('description', 'N/A')}")
            
            print("\n📁 生成的文件列表:")
            for file in result["file_list"]:
                print(f"   - {file}")
            
            print("\n✅ 项目验证结果:")
            validation = result["validation"]
            if validation.get("is_valid", True):
                print("   项目验证通过 ✅")
            else:
                print("   项目验证发现问题 ⚠️")
                for error in validation.get("errors", []):
                    print(f"     - {error}")
            
            # 检查生成的文件内容
            print("\n🔍 检查生成的文件内容:")
            check_generated_files(result['project_path'])
            
            print(f"\n🚀 使用微信开发者工具导入步骤:")
            print(f"   1. 打开微信开发者工具")
            print(f"   2. 选择'导入项目'")
            print(f"   3. 选择项目目录: {result['project_path']}")
            print(f"   4. 填写AppID（可使用测试号）")
            print(f"   5. 点击'导入'并编译运行")
            
            return True
            
        else:
            print(f"\n❌ 天气预报小程序生成失败!")
            print(f"错误信息: {result.get('error', '未知错误')}")
            return False
            
    except Exception as e:
        print(f"\n💥 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return False


def check_generated_files(project_path):
    """检查生成的文件内容"""
    try:
        # 检查app.json
        app_json_path = os.path.join(project_path, "app.json")
        if os.path.exists(app_json_path):
            with open(app_json_path, 'r', encoding='utf-8') as f:
                content = f.read()
                if content.strip():
                    print(f"   ✅ app.json 文件内容正常 ({len(content)} 字符)")
                else:
                    print(f"   ⚠️  app.json 文件为空")
        else:
            print(f"   ❌ app.json 文件不存在")
        
        # 检查页面文件
        pages_dir = os.path.join(project_path, "pages")
        if os.path.exists(pages_dir):
            for page_dir in os.listdir(pages_dir):
                page_path = os.path.join(pages_dir, page_dir)
                if os.path.isdir(page_path):
                    files = os.listdir(page_path)
                    if files:
                        print(f"   ✅ 页面 {page_dir} 包含 {len(files)} 个文件")
                    else:
                        print(f"   ⚠️  页面 {page_dir} 为空")
        
        # 检查工具文件
        utils_dir = os.path.join(project_path, "utils")
        if os.path.exists(utils_dir):
            utils_files = os.listdir(utils_dir)
            if utils_files:
                print(f"   ✅ utils目录包含 {len(utils_files)} 个文件")
            else:
                print(f"   ⚠️  utils目录为空")
                
    except Exception as e:
        print(f"   ❌ 检查文件时出错: {e}")


def check_api_config():
    """检查API配置"""
    print("🔍 检查API配置...")
    
    from config import API_CONFIGS
    
    all_configured = True
    for model_name, config in API_CONFIGS.items():
        api_key = config.get("api_key", "")
        if api_key and api_key != f"your-{model_name}-api-key":
            print(f"   ✅ {model_name.upper()} API密钥已配置")
        else:
            print(f"   ❌ {model_name.upper()} API密钥未配置")
            all_configured = False
    
    print()
    return all_configured


if __name__ == "__main__":
    print("🧪 微信小程序自动生成系统 - 改进版API测试")
    print("=" * 60)
    
    # 检查API配置
    if not check_api_config():
        print("❌ 请先配置API密钥后再运行测试")
        sys.exit(1)
    
    # 测试生成天气预报小程序
    success = test_weather_forecast_improved()
    
    if success:
        print("\n🎊 测试完成！改进版天气预报小程序生成成功！")
        print("\n📈 改进效果:")
        print("   ✅ API调用更稳定")
        print("   ✅ 错误处理更完善")
        print("   ✅ 重试机制有效")
        print("   ✅ 超时时间合理")
    else:
        print("\n😞 测试失败，请检查网络连接和API配置")
