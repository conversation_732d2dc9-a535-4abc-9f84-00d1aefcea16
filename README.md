# 微信小程序自动生成系统

🚀 基于自然语言需求自动生成完整微信小程序项目的智能系统

## 系统特性

- 🤖 **多Agent协作**: Manager、CodeWriter、FileManager三个智能Agent协同工作
- 🧠 **多模型支持**: 集成qwen、deepseek、kimi三种大模型，各司其职
- 📱 **完整项目**: 生成可直接导入微信开发者工具运行的完整项目
- 🎯 **自然语言**: 支持自然语言描述需求，自动分析和生成代码
- 📁 **规范结构**: 遵循微信小程序开发规范，生成标准项目结构
- 🔄 **智能重试**: API调用失败自动重试，提高系统稳定性
- ⚡ **连接优化**: 使用连接池和会话复用，提升性能

## 系统架构

### Agent分工

1. **Manager Agent** (qwen模型)
   - 任务协调者
   - 需求分析和任务分解
   - 项目验证和质量控制

2. **CodeWriter Agent** (deepseek模型)
   - 代码生成器
   - 生成WXML、WXSS、JS、JSON代码
   - 专业的微信小程序开发知识

3. **FileManager Agent** (kimi模型)
   - 文件管理器
   - 创建项目结构
   - 组织和保存文件

## 快速开始

### 安装依赖
```bash
pip install -r requirements.txt
```

### 配置API密钥
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑.env文件，填入真实的API密钥
QWEN_API_KEY=your-qwen-api-key
DEEPSEEK_API_KEY=your-deepseek-api-key
KIMI_API_KEY=your-kimi-api-key
```

### 运行系统
```bash
# 交互式运行
python main.py

# 或运行改进版测试
python test_improved.py
```

## 使用示例

### 输入需求
```
我想要一个待办事项管理的微信小程序，功能包括：
1. 首页显示待办事项列表
2. 可以添加新的待办事项
3. 可以标记待办事项为完成
4. 可以删除待办事项
5. 数据保存在本地存储中
```

### 生成结果
```
project/
├── todo-app/
│   ├── app.json          # 应用配置
│   ├── app.js            # 应用逻辑
│   ├── app.wxss          # 全局样式
│   ├── pages/
│   │   ├── index/        # 首页
│   │   │   ├── index.js
│   │   │   ├── index.wxml
│   │   │   ├── index.wxss
│   │   │   └── index.json
│   │   └── todo/         # 待办页面
│   │       ├── index.js
│   │       ├── index.wxml
│   │       ├── index.wxss
│   │       └── index.json
│   └── utils/
│       └── storage.js    # 本地存储工具
```

## 生成的代码特性

### 页面代码 (pages/todo/index.js)
- ✅ 完整的增删改查逻辑
- ✅ 本地存储数据持久化
- ✅ 用户交互和反馈
- ✅ 错误处理和验证

### 界面布局 (pages/todo/index.wxml)
- ✅ 响应式设计
- ✅ 用户友好的交互界面
- ✅ 完整的数据绑定
- ✅ 条件渲染和列表渲染

### 样式设计 (pages/todo/index.wxss)
- ✅ 现代化UI设计
- ✅ 适配不同屏幕尺寸
- ✅ 流畅的动画效果
- ✅ 符合微信设计规范

### 工具类 (utils/storage.js)
- ✅ 完整的本地存储封装
- ✅ 错误处理和异常捕获
- ✅ 简洁的API接口
- ✅ 详细的代码注释

### 应用配置 (app.json)
- ✅ 页面路由配置
- ✅ 窗口样式设置
- ✅ 底部导航栏配置
- ✅ 网络超时设置

## 项目结构

```
autogen-miniprogram/
├── config.py              # 配置文件
├── manager_agent.py        # 管理Agent
├── code_writer_agent.py    # 代码生成Agent
├── file_manager_agent.py   # 文件管理Agent
├── main.py                 # 主程序
├── simple_generator.py     # 简化版生成器
├── demo.py                 # 演示程序
├── requirements.txt        # 依赖包
├── .env.example           # 环境变量模板
├── README.md              # 说明文档
└── project/               # 生成的项目目录
    └── [生成的小程序项目]
```

## API配置

### 获取API密钥

1. **Qwen API**: 访问 [阿里云百炼](https://bailian.console.aliyun.com/)
2. **DeepSeek API**: 访问 [DeepSeek开放平台](https://platform.deepseek.com/)
3. **Kimi API**: 访问 [Moonshot AI](https://platform.moonshot.cn/)

### 配置说明

在 `config.py` 中可以调整：
- API端点和模型选择
- 项目输出目录
- 代码生成提示模板
- 超时和重试设置

## 导入微信开发者工具

1. 打开微信开发者工具
2. 选择"导入项目"
3. 选择生成的项目文件夹（如 `project/todo-app/`）
4. 填写AppID（测试号即可）
5. 点击"导入"
6. 编译运行

## 扩展功能

### 添加新的Agent
```python
class CustomAgent:
    def __init__(self):
        # 初始化自定义Agent
        pass
    
    def process(self, input_data):
        # 处理逻辑
        return result
```

### 自定义代码模板
在 `config.py` 的 `CODE_GENERATION_PROMPTS` 中添加新模板：
```python
"custom_template": """
自定义提示模板内容...
{description}
"""
```

### 添加新的页面类型
扩展 `CodeWriterAgent` 的生成方法：
```python
def generate_custom_page(self, page_name, description):
    # 自定义页面生成逻辑
    return code_files
```

## 常见问题

### Q: 生成的代码可以直接运行吗？
A: 是的，生成的代码遵循微信小程序开发规范，可以直接导入微信开发者工具运行。

### Q: 支持哪些类型的小程序？
A: 支持各种类型的小程序，如工具类、电商类、内容类、游戏类等。

### Q: 可以自定义生成的代码风格吗？
A: 可以通过修改 `config.py` 中的提示模板来自定义代码风格。

### Q: 生成的项目如何部署？
A: 使用微信开发者工具上传代码，然后在微信公众平台提交审核发布。

## 贡献指南

欢迎提交Issue和Pull Request来改进这个项目！

## 许可证

MIT License

---

🎯 **让AI帮你快速构建微信小程序，从想法到上线只需几分钟！**
