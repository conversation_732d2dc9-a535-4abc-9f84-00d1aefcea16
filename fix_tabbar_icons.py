# -*- coding: utf-8 -*-
"""
修复tabBar图标问题
提供两种解决方案：移除tabBar或创建默认图标
"""

import os
import json
import base64
from PIL import Image, ImageDraw


def create_default_icons():
    """创建默认的tabBar图标"""
    images_dir = "project/weather-forecast/images"
    os.makedirs(images_dir, exist_ok=True)
    
    # 创建天气图标
    create_weather_icon(os.path.join(images_dir, "weather.png"), False)
    create_weather_icon(os.path.join(images_dir, "weather_active.png"), True)
    
    # 创建详情图标
    create_detail_icon(os.path.join(images_dir, "detail.png"), False)
    create_detail_icon(os.path.join(images_dir, "detail_active.png"), True)
    
    print("✅ 创建默认图标完成")


def create_weather_icon(filepath, active=False):
    """创建天气图标"""
    # 创建81x81的图标（微信小程序推荐尺寸）
    size = (81, 81)
    img = Image.new('RGBA', size, (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # 设置颜色
    color = "#4A90E2" if active else "#7A7E83"
    
    # 绘制太阳（圆形）
    center = (40, 35)
    radius = 15
    draw.ellipse([center[0]-radius, center[1]-radius, 
                  center[0]+radius, center[1]+radius], 
                 fill=color)
    
    # 绘制太阳光线
    for i in range(8):
        angle = i * 45
        import math
        x1 = center[0] + (radius + 5) * math.cos(math.radians(angle))
        y1 = center[1] + (radius + 5) * math.sin(math.radians(angle))
        x2 = center[0] + (radius + 12) * math.cos(math.radians(angle))
        y2 = center[1] + (radius + 12) * math.sin(math.radians(angle))
        draw.line([x1, y1, x2, y2], fill=color, width=2)
    
    # 绘制云朵
    cloud_y = 55
    draw.ellipse([20, cloud_y, 35, cloud_y+10], fill=color)
    draw.ellipse([30, cloud_y-3, 50, cloud_y+7], fill=color)
    draw.ellipse([45, cloud_y, 60, cloud_y+10], fill=color)
    
    img.save(filepath, "PNG")


def create_detail_icon(filepath, active=False):
    """创建详情图标"""
    size = (81, 81)
    img = Image.new('RGBA', size, (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # 设置颜色
    color = "#4A90E2" if active else "#7A7E83"
    
    # 绘制列表图标
    # 绘制三个横线表示列表
    for i in range(3):
        y = 25 + i * 15
        draw.rectangle([20, y, 60, y+3], fill=color)
    
    # 绘制左侧的小圆点
    for i in range(3):
        y = 25 + i * 15 + 1
        draw.ellipse([12, y, 16, y+4], fill=color)
    
    img.save(filepath, "PNG")


def remove_tabbar_config():
    """移除tabBar配置（简单方案）"""
    app_json_path = "project/weather-forecast/app.json"
    
    try:
        with open(app_json_path, 'r', encoding='utf-8') as f:
            app_config = json.load(f)
        
        # 移除tabBar配置
        if "tabBar" in app_config:
            del app_config["tabBar"]
            print("✅ 移除tabBar配置")
        
        # 保存修改后的配置
        with open(app_json_path, 'w', encoding='utf-8') as f:
            json.dump(app_config, f, ensure_ascii=False, indent=2)
        
        print("✅ app.json更新完成")
        
    except Exception as e:
        print(f"❌ 修改app.json失败: {e}")


def create_simple_icons():
    """创建简单的纯色图标（不依赖PIL）"""
    images_dir = "project/weather-forecast/images"
    os.makedirs(images_dir, exist_ok=True)
    
    # 创建简单的PNG图标（使用base64编码的最小PNG）
    # 这是一个1x1像素的透明PNG
    simple_png_data = base64.b64decode(
        'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg=='
    )
    
    # 创建所有需要的图标文件
    icon_files = [
        "weather.png",
        "weather_active.png", 
        "detail.png",
        "detail_active.png"
    ]
    
    for icon_file in icon_files:
        icon_path = os.path.join(images_dir, icon_file)
        with open(icon_path, 'wb') as f:
            f.write(simple_png_data)
    
    print("✅ 创建简单图标完成")


def main():
    """主函数"""
    print("🔧 修复tabBar图标问题")
    print("=" * 50)
    
    print("请选择解决方案:")
    print("1. 移除tabBar配置（推荐，简单快速）")
    print("2. 创建简单的占位图标")
    print("3. 创建精美的图标（需要PIL库）")
    
    try:
        choice = input("\n请输入选择 (1-3): ").strip()
    except:
        choice = "1"  # 默认选择1
    
    if choice == "1":
        print("\n🔧 移除tabBar配置...")
        remove_tabbar_config()
        print("\n✅ 修复完成！现在小程序将使用单页面模式，没有底部导航栏。")
        
    elif choice == "2":
        print("\n🔧 创建简单占位图标...")
        create_simple_icons()
        print("\n✅ 修复完成！创建了简单的占位图标，tabBar可以正常显示。")
        print("💡 提示：您可以后续替换为自定义的图标文件。")
        
    elif choice == "3":
        print("\n🔧 创建精美图标...")
        try:
            create_default_icons()
            print("\n✅ 修复完成！创建了精美的天气和详情图标。")
        except ImportError:
            print("❌ 缺少PIL库，正在使用简单图标方案...")
            create_simple_icons()
            print("\n✅ 修复完成！创建了简单的占位图标。")
            print("💡 提示：如需精美图标，请安装PIL库：pip install Pillow")
        except Exception as e:
            print(f"❌ 创建图标失败: {e}")
            print("🔧 回退到简单图标方案...")
            create_simple_icons()
            print("✅ 使用简单图标修复完成")
    
    else:
        print("❌ 无效选择，使用默认方案（移除tabBar）")
        remove_tabbar_config()
    
    print(f"\n🚀 现在可以重新导入微信开发者工具了！")
    print(f"📁 项目路径: project/weather-forecast")


# 自动运行，选择方案1（移除tabBar）
if __name__ == "__main__":
    print("🔧 自动修复tabBar图标问题")
    print("选择方案1：移除tabBar配置（推荐）")
    
    remove_tabbar_config()
    
    print("\n✅ 修复完成！")
    print("📝 修改说明：")
    print("   - 移除了底部导航栏配置")
    print("   - 小程序将以单页面模式运行")
    print("   - 用户可以通过页面内的按钮或链接进行导航")
    print(f"\n🚀 现在可以重新导入微信开发者工具了！")
