# -*- coding: utf-8 -*-
"""
项目验证和修复工具
检查生成的微信小程序项目是否完整，如果有问题则自动修复
"""

import os
import json
import shutil


def validate_miniprogram_project(project_path):
    """验证微信小程序项目完整性"""
    print(f"🔍 验证项目: {project_path}")
    
    issues = []
    
    # 检查必需文件
    required_files = ["app.json", "app.js", "app.wxss"]
    for file in required_files:
        file_path = os.path.join(project_path, file)
        if not os.path.exists(file_path):
            issues.append(f"缺少必需文件: {file}")
        elif os.path.getsize(file_path) == 0:
            issues.append(f"文件为空: {file}")
    
    # 检查pages目录
    pages_dir = os.path.join(project_path, "pages")
    if not os.path.exists(pages_dir):
        issues.append("缺少pages目录")
    else:
        # 检查是否有页面
        page_dirs = [d for d in os.listdir(pages_dir) if os.path.isdir(os.path.join(pages_dir, d))]
        if not page_dirs:
            issues.append("pages目录为空")
        else:
            # 检查每个页面的必需文件
            for page_dir in page_dirs:
                page_path = os.path.join(pages_dir, page_dir)
                required_page_files = [f"{page_dir}.js", f"{page_dir}.wxml", f"{page_dir}.wxss", f"{page_dir}.json"]
                for page_file in required_page_files:
                    page_file_path = os.path.join(page_path, page_file)
                    if not os.path.exists(page_file_path):
                        issues.append(f"页面{page_dir}缺少文件: {page_file}")
    
    # 检查app.json格式
    app_json_path = os.path.join(project_path, "app.json")
    if os.path.exists(app_json_path):
        try:
            with open(app_json_path, 'r', encoding='utf-8') as f:
                app_config = json.load(f)
            
            if "pages" not in app_config:
                issues.append("app.json缺少pages配置")
            elif not app_config["pages"]:
                issues.append("app.json的pages配置为空")
                
        except json.JSONDecodeError:
            issues.append("app.json格式错误")
    
    return issues


def fix_miniprogram_project(project_path):
    """修复微信小程序项目"""
    print(f"🔧 修复项目: {project_path}")
    
    # 确保基本目录存在
    os.makedirs(os.path.join(project_path, "pages"), exist_ok=True)
    os.makedirs(os.path.join(project_path, "utils"), exist_ok=True)
    os.makedirs(os.path.join(project_path, "components"), exist_ok=True)
    os.makedirs(os.path.join(project_path, "images"), exist_ok=True)
    
    # 检查并修复页面目录
    pages_dir = os.path.join(project_path, "pages")
    page_dirs = []
    
    # 收集现有页面目录
    if os.path.exists(pages_dir):
        for item in os.listdir(pages_dir):
            item_path = os.path.join(pages_dir, item)
            if os.path.isdir(item_path):
                page_dirs.append(item)
    
    # 如果没有页面，创建默认页面
    if not page_dirs:
        create_default_page(os.path.join(pages_dir, "index"), "index")
        page_dirs = ["index"]
    
    # 修复页面文件
    for page_dir in page_dirs:
        fix_page_files(os.path.join(pages_dir, page_dir), page_dir)
    
    # 生成或修复app.json
    generate_app_json(project_path, page_dirs)
    
    # 生成或修复app.js
    generate_app_js(project_path)
    
    # 生成或修复app.wxss
    generate_app_wxss(project_path)
    
    print("✅ 项目修复完成")


def create_default_page(page_path, page_name):
    """创建默认页面"""
    os.makedirs(page_path, exist_ok=True)
    
    # 生成页面文件
    files = {
        f"{page_name}.js": f'''// {page_name}.js
Page({{
  data: {{
    title: '欢迎使用微信小程序'
  }},
  
  onLoad: function(options) {{
    console.log('页面加载完成');
  }}
}});''',
        
        f"{page_name}.wxml": f'''<!--{page_name}.wxml-->
<view class="container">
  <view class="title">{{{{title}}}}</view>
  <view class="content">
    <text>这是一个自动生成的微信小程序页面</text>
  </view>
</view>''',
        
        f"{page_name}.wxss": f'''/* {page_name}.wxss */
.container {{
  padding: 20rpx;
  min-height: 100vh;
  background-color: #f5f5f5;
}}

.title {{
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 40rpx;
  color: #333;
}}

.content {{
  background-color: #fff;
  padding: 30rpx;
  border-radius: 10rpx;
  text-align: center;
}}''',
        
        f"{page_name}.json": '''{
  "navigationBarTitleText": "页面标题"
}'''
    }
    
    for filename, content in files.items():
        file_path = os.path.join(page_path, filename)
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)


def fix_page_files(page_path, page_name):
    """修复页面文件"""
    if not os.path.exists(page_path):
        create_default_page(page_path, page_name)
        return
    
    required_files = [f"{page_name}.js", f"{page_name}.wxml", f"{page_name}.wxss", f"{page_name}.json"]
    
    for filename in required_files:
        file_path = os.path.join(page_path, filename)
        if not os.path.exists(file_path) or os.path.getsize(file_path) == 0:
            # 生成默认内容
            if filename.endswith('.js'):
                content = f'''// {filename}
Page({{
  data: {{}},
  onLoad: function(options) {{
    console.log('页面加载');
  }}
}});'''
            elif filename.endswith('.wxml'):
                content = f'''<!--{filename}-->
<view class="container">
  <text>页面内容</text>
</view>'''
            elif filename.endswith('.wxss'):
                content = f'''/* {filename} */
.container {{
  padding: 20rpx;
}}'''
            elif filename.endswith('.json'):
                content = '''{
  "navigationBarTitleText": "页面"
}'''
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)


def generate_app_json(project_path, page_dirs):
    """生成app.json"""
    pages_list = [f"pages/{page_dir}/{page_dir}" for page_dir in page_dirs]
    
    app_config = {
        "pages": pages_list,
        "window": {
            "backgroundTextStyle": "light",
            "navigationBarBackgroundColor": "#4A90E2",
            "navigationBarTitleText": "微信小程序",
            "navigationBarTextStyle": "white",
            "backgroundColor": "#f8f8f8"
        },
        "networkTimeout": {
            "request": 10000,
            "downloadFile": 10000
        },
        "debug": True
    }
    
    app_json_path = os.path.join(project_path, "app.json")
    with open(app_json_path, 'w', encoding='utf-8') as f:
        json.dump(app_config, f, ensure_ascii=False, indent=2)


def generate_app_js(project_path):
    """生成app.js"""
    content = '''// app.js
App({
  onLaunch: function () {
    console.log('小程序启动');
  },
  
  onShow: function () {
    console.log('小程序显示');
  },
  
  onHide: function () {
    console.log('小程序隐藏');
  },
  
  onError: function (msg) {
    console.error('小程序错误:', msg);
  },
  
  globalData: {
    userInfo: null,
    appName: '微信小程序',
    version: '1.0.0'
  }
});'''
    
    app_js_path = os.path.join(project_path, "app.js")
    with open(app_js_path, 'w', encoding='utf-8') as f:
        f.write(content)


def generate_app_wxss(project_path):
    """生成app.wxss"""
    content = '''/* app.wxss */
page {
  background-color: #f8f8f8;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

.container {
  padding: 20rpx;
  min-height: 100vh;
}

.btn {
  background-color: #4A90E2;
  color: white;
  border-radius: 8rpx;
  padding: 20rpx 40rpx;
  margin: 20rpx 0;
  font-size: 28rpx;
  border: none;
}

.btn:active {
  background-color: #357ABD;
}'''
    
    app_wxss_path = os.path.join(project_path, "app.wxss")
    with open(app_wxss_path, 'w', encoding='utf-8') as f:
        f.write(content)


def main():
    """主函数"""
    project_path = "project/weather-forecast"
    
    if not os.path.exists(project_path):
        print(f"❌ 项目路径不存在: {project_path}")
        return
    
    print("🔍 开始验证和修复微信小程序项目...")
    
    # 验证项目
    issues = validate_miniprogram_project(project_path)
    
    if issues:
        print(f"\n⚠️  发现 {len(issues)} 个问题:")
        for issue in issues:
            print(f"   - {issue}")
        
        print(f"\n🔧 开始修复...")
        fix_miniprogram_project(project_path)
        
        # 再次验证
        print(f"\n🔍 修复后验证...")
        new_issues = validate_miniprogram_project(project_path)
        
        if new_issues:
            print(f"⚠️  仍有 {len(new_issues)} 个问题:")
            for issue in new_issues:
                print(f"   - {issue}")
        else:
            print("✅ 所有问题已修复！")
    else:
        print("✅ 项目验证通过，无需修复")
    
    print(f"\n🚀 项目可以导入微信开发者工具了！")
    print(f"📁 项目路径: {os.path.abspath(project_path)}")


if __name__ == "__main__":
    main()
