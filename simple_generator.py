# -*- coding: utf-8 -*-
"""
简化版微信小程序生成器 - 无需API密钥
直接生成基础的微信小程序项目结构和代码
"""

import os
import json
from typing import Dict, List, Any


class SimpleMiniProgramGenerator:
    """简化版微信小程序生成器"""
    
    def __init__(self):
        self.output_dir = "project"
    
    def generate_project(self, description: str, project_name: str = "miniprogram") -> bool:
        """生成微信小程序项目"""
        try:
            print(f"🚀 开始生成微信小程序项目: {project_name}")
            
            # 创建项目目录
            project_path = os.path.join(self.output_dir, project_name)
            os.makedirs(project_path, exist_ok=True)
            
            # 创建子目录
            os.makedirs(os.path.join(project_path, "pages", "index"), exist_ok=True)
            os.makedirs(os.path.join(project_path, "pages", "todo"), exist_ok=True)
            os.makedirs(os.path.join(project_path, "utils"), exist_ok=True)
            os.makedirs(os.path.join(project_path, "images"), exist_ok=True)
            
            # 生成app.json
            self._create_app_json(project_path, description)
            
            # 生成app.js
            self._create_app_js(project_path, project_name)
            
            # 生成app.wxss
            self._create_app_wxss(project_path)
            
            # 生成首页
            self._create_index_page(project_path)
            
            # 生成待办页面
            self._create_todo_page(project_path)
            
            # 生成工具类
            self._create_storage_util(project_path)
            
            print(f"✅ 项目生成完成: {project_path}")
            return True
            
        except Exception as e:
            print(f"❌ 生成项目失败: {e}")
            return False
    
    def _create_app_json(self, project_path: str, description: str):
        """创建app.json"""
        config = {
            "pages": [
                "pages/index/index",
                "pages/todo/index"
            ],
            "window": {
                "backgroundTextStyle": "light",
                "navigationBarBackgroundColor": "#fff",
                "navigationBarTitleText": "微信小程序",
                "navigationBarTextStyle": "black"
            },
            "tabBar": {
                "color": "#7A7E83",
                "selectedColor": "#3cc51f",
                "borderStyle": "black",
                "backgroundColor": "#ffffff",
                "list": [
                    {
                        "pagePath": "pages/index/index",
                        "iconPath": "images/icon_component.png",
                        "selectedIconPath": "images/icon_component_HL.png",
                        "text": "首页"
                    },
                    {
                        "pagePath": "pages/todo/index",
                        "iconPath": "images/icon_API.png",
                        "selectedIconPath": "images/icon_API_HL.png",
                        "text": "待办"
                    }
                ]
            },
            "networkTimeout": {
                "request": 10000,
                "downloadFile": 10000
            },
            "debug": True
        }
        
        with open(os.path.join(project_path, "app.json"), 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
    
    def _create_app_js(self, project_path: str, project_name: str):
        """创建app.js"""
        content = f'''// app.js
App({{
  onLaunch() {{
    console.log('{project_name} 启动');
    
    // 展示本地存储能力
    const logs = wx.getStorageSync('logs') || []
    logs.unshift(Date.now())
    wx.setStorageSync('logs', logs)

    // 登录
    wx.login({{
      success: res => {{
        console.log('登录成功', res.code);
      }}
    }})
  }},
  
  globalData: {{
    userInfo: null,
    appName: '{project_name}',
    version: '1.0.0'
  }}
}})'''
        
        with open(os.path.join(project_path, "app.js"), 'w', encoding='utf-8') as f:
            f.write(content)
    
    def _create_app_wxss(self, project_path: str):
        """创建app.wxss"""
        content = '''/**app.wxss**/
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 200rpx 0;
  box-sizing: border-box;
}

/* 通用样式 */
page {
  background-color: #f5f5f5;
}

.btn {
  background-color: #1aad19;
  color: white;
  border-radius: 8rpx;
  padding: 20rpx 40rpx;
  margin: 20rpx;
  font-size: 28rpx;
}

.btn:active {
  background-color: #179b16;
}

.card {
  background-color: white;
  margin: 20rpx;
  padding: 30rpx;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.text-center {
  text-align: center;
}

.mt-20 {
  margin-top: 40rpx;
}

.mb-20 {
  margin-bottom: 40rpx;
}'''
        
        with open(os.path.join(project_path, "app.wxss"), 'w', encoding='utf-8') as f:
            f.write(content)
    
    def _create_index_page(self, project_path: str):
        """创建首页"""
        # index.js
        js_content = '''// pages/index/index.js
Page({
  data: {
    motto: 'Hello World',
    userInfo: {},
    hasUserInfo: false,
    canIUseGetUserProfile: wx.canIUse('getUserProfile'),
    canIUseNicknameComp: wx.canIUse('input.type.nickname'),
  },

  onLoad() {
    if (wx.getUserProfile) {
      this.setData({
        canIUseGetUserProfile: true
      })
    }
  },

  getUserProfile(e) {
    wx.getUserProfile({
      desc: '用于完善会员资料',
      success: (res) => {
        console.log(res)
        this.setData({
          userInfo: res.userInfo,
          hasUserInfo: true
        })
      }
    })
  },

  bindViewTap() {
    wx.navigateTo({
      url: '../todo/index'
    })
  },
})'''
        
        # index.wxml
        wxml_content = '''<!--pages/index/index.wxml-->
<view class="container">
  <view class="userinfo">
    <block wx:if="{{canIUseNicknameComp && !hasUserInfo}}">
      <button class="avatar-wrapper" open-type="chooseAvatar" bind:chooseavatar="onChooseAvatar">
        <image class="avatar" src="{{userInfo.avatarUrl}}"></image>
      </button>
      <view class="nickname-wrapper">
        <text class="nickname-label">昵称</text>
        <input type="nickname" class="nickname-input" placeholder="请输入昵称" bind:change="onInputChange" />
      </view>
    </block>
    <block wx:elif="{{!hasUserInfo}}">
      <button wx:if="{{canIUseGetUserProfile}}" bindtap="getUserProfile"> 获取头像昵称 </button>
      <view wx:else> 请使用2.10.4及以上版本基础库 </view>
    </block>
    <block wx:else>
      <image bindtap="bindViewTap" class="userinfo-avatar" src="{{userInfo.avatarUrl}}" mode="cover"></image>
      <text class="userinfo-nickname">{{userInfo.nickName}}</text>
    </block>
  </view>
  <view class="usermotto">
    <text class="user-motto">{{motto}}</text>
  </view>
  
  <view class="card">
    <text class="title">欢迎使用微信小程序</text>
    <view class="mt-20">
      <button class="btn" bindtap="bindViewTap">进入待办管理</button>
    </view>
  </view>
</view>'''
        
        # index.wxss
        wxss_content = '''/**pages/index/index.wxss**/
.userinfo {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #aaa;
  width: 80%;
}

.userinfo-avatar {
  overflow: hidden;
  width: 128rpx;
  height: 128rpx;
  margin: 20rpx;
  border-radius: 50%;
}

.usermotto {
  margin-top: 200rpx;
}

.avatar-wrapper {
  padding: 0;
  width: 56px !important;
  border-radius: 8px;
  margin-top: 40px;
  margin-bottom: 40px;
}

.avatar {
  display: block;
  width: 56px;
  height: 56px;
}

.nickname-wrapper {
  display: flex;
  width: 100%;
  padding: 16px;
  box-sizing: border-box;
  border-top: .5px solid rgba(0, 0, 0, 0.1);
  border-bottom: .5px solid rgba(0, 0, 0, 0.1);
  color: black;
}

.nickname-label {
  width: 105px;
}

.nickname-input {
  flex: 1;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}'''
        
        # index.json
        json_content = '''{
  "navigationBarTitleText": "首页"
}'''
        
        # 保存文件
        index_dir = os.path.join(project_path, "pages", "index")
        with open(os.path.join(index_dir, "index.js"), 'w', encoding='utf-8') as f:
            f.write(js_content)
        with open(os.path.join(index_dir, "index.wxml"), 'w', encoding='utf-8') as f:
            f.write(wxml_content)
        with open(os.path.join(index_dir, "index.wxss"), 'w', encoding='utf-8') as f:
            f.write(wxss_content)
        with open(os.path.join(index_dir, "index.json"), 'w', encoding='utf-8') as f:
            f.write(json_content)
    
    def _create_todo_page(self, project_path: str):
        """创建待办页面"""
        # index.js
        js_content = '''// pages/todo/index.js
const storage = require('../../utils/storage.js')

Page({
  data: {
    todos: [],
    inputValue: '',
    nextId: 1
  },

  onLoad() {
    this.loadTodos()
  },

  // 加载待办事项
  loadTodos() {
    const todos = storage.get('todos', [])
    const nextId = storage.get('nextId', 1)
    this.setData({
      todos,
      nextId
    })
  },

  // 保存待办事项
  saveTodos() {
    storage.set('todos', this.data.todos)
    storage.set('nextId', this.data.nextId)
  },

  // 输入框变化
  onInputChange(e) {
    this.setData({
      inputValue: e.detail.value
    })
  },

  // 添加待办事项
  addTodo() {
    const { inputValue, todos, nextId } = this.data
    if (!inputValue.trim()) {
      wx.showToast({
        title: '请输入待办事项',
        icon: 'none'
      })
      return
    }

    const newTodo = {
      id: nextId,
      text: inputValue.trim(),
      completed: false,
      createTime: new Date().toLocaleString()
    }

    this.setData({
      todos: [...todos, newTodo],
      inputValue: '',
      nextId: nextId + 1
    })

    this.saveTodos()
    
    wx.showToast({
      title: '添加成功',
      icon: 'success'
    })
  },

  // 切换完成状态
  toggleTodo(e) {
    const { id } = e.currentTarget.dataset
    const todos = this.data.todos.map(todo => {
      if (todo.id === id) {
        return { ...todo, completed: !todo.completed }
      }
      return todo
    })

    this.setData({ todos })
    this.saveTodos()
  },

  // 删除待办事项
  deleteTodo(e) {
    const { id } = e.currentTarget.dataset
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个待办事项吗？',
      success: (res) => {
        if (res.confirm) {
          const todos = this.data.todos.filter(todo => todo.id !== id)
          this.setData({ todos })
          this.saveTodos()
          
          wx.showToast({
            title: '删除成功',
            icon: 'success'
          })
        }
      }
    })
  },

  // 清空所有
  clearAll() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有待办事项吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            todos: [],
            nextId: 1
          })
          this.saveTodos()
          
          wx.showToast({
            title: '清空成功',
            icon: 'success'
          })
        }
      }
    })
  }
})'''
        
        # index.wxml
        wxml_content = '''<!--pages/todo/index.wxml-->
<view class="container">
  <!-- 输入区域 -->
  <view class="input-section">
    <view class="input-wrapper">
      <input 
        class="todo-input" 
        placeholder="输入待办事项..." 
        value="{{inputValue}}"
        bindinput="onInputChange"
        confirm-type="done"
        bindconfirm="addTodo"
      />
      <button class="add-btn" bindtap="addTodo">添加</button>
    </view>
  </view>

  <!-- 统计信息 -->
  <view class="stats">
    <text class="stats-text">
      共 {{todos.length}} 项，已完成 {{todos.filter(item => item.completed).length}} 项
    </text>
    <button wx:if="{{todos.length > 0}}" class="clear-btn" bindtap="clearAll">清空</button>
  </view>

  <!-- 待办列表 -->
  <view class="todo-list">
    <view 
      wx:for="{{todos}}" 
      wx:key="id" 
      class="todo-item {{item.completed ? 'completed' : ''}}"
    >
      <view class="todo-content" bindtap="toggleTodo" data-id="{{item.id}}">
        <view class="checkbox {{item.completed ? 'checked' : ''}}">
          <text wx:if="{{item.completed}}" class="checkmark">✓</text>
        </view>
        <view class="todo-text">
          <text class="text">{{item.text}}</text>
          <text class="time">{{item.createTime}}</text>
        </view>
      </view>
      <view class="todo-actions">
        <button class="delete-btn" bindtap="deleteTodo" data-id="{{item.id}}">删除</button>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view wx:if="{{todos.length === 0}}" class="empty-state">
    <text class="empty-text">暂无待办事项</text>
    <text class="empty-tip">点击上方输入框添加新的待办事项</text>
  </view>
</view>'''
        
        # index.wxss
        wxss_content = '''/* pages/todo/index.wxss */
.container {
  padding: 0;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.input-section {
  background-color: white;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.input-wrapper {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.todo-input {
  flex: 1;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #ddd;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.add-btn {
  background-color: #1aad19;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 0 30rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
}

.add-btn:active {
  background-color: #179b16;
}

.stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: white;
  border-bottom: 1rpx solid #eee;
}

.stats-text {
  font-size: 24rpx;
  color: #666;
}

.clear-btn {
  background-color: #ff4757;
  color: white;
  border: none;
  border-radius: 6rpx;
  padding: 10rpx 20rpx;
  font-size: 24rpx;
}

.todo-list {
  padding: 20rpx 0;
}

.todo-item {
  display: flex;
  align-items: center;
  background-color: white;
  margin: 0 30rpx 20rpx;
  padding: 30rpx;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.todo-item.completed {
  opacity: 0.6;
}

.todo-content {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.checkbox {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.checkbox.checked {
  background-color: #1aad19;
  border-color: #1aad19;
}

.checkmark {
  color: white;
  font-size: 24rpx;
  font-weight: bold;
}

.todo-text {
  flex: 1;
}

.text {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.completed .text {
  text-decoration: line-through;
  color: #999;
}

.time {
  font-size: 22rpx;
  color: #999;
}

.todo-actions {
  margin-left: 20rpx;
}

.delete-btn {
  background-color: #ff4757;
  color: white;
  border: none;
  border-radius: 6rpx;
  padding: 10rpx 20rpx;
  font-size: 24rpx;
}

.empty-state {
  text-align: center;
  padding: 100rpx 30rpx;
}

.empty-text {
  display: block;
  font-size: 32rpx;
  color: #999;
  margin-bottom: 20rpx;
}

.empty-tip {
  font-size: 24rpx;
  color: #ccc;
}'''
        
        # index.json
        json_content = '''{
  "navigationBarTitleText": "待办管理"
}'''
        
        # 保存文件
        todo_dir = os.path.join(project_path, "pages", "todo")
        with open(os.path.join(todo_dir, "index.js"), 'w', encoding='utf-8') as f:
            f.write(js_content)
        with open(os.path.join(todo_dir, "index.wxml"), 'w', encoding='utf-8') as f:
            f.write(wxml_content)
        with open(os.path.join(todo_dir, "index.wxss"), 'w', encoding='utf-8') as f:
            f.write(wxss_content)
        with open(os.path.join(todo_dir, "index.json"), 'w', encoding='utf-8') as f:
            f.write(json_content)
    
    def _create_storage_util(self, project_path: str):
        """创建存储工具"""
        content = '''// utils/storage.js
// 本地存储工具类
const storage = {
  // 设置存储
  set(key, value) {
    try {
      wx.setStorageSync(key, value);
      return true;
    } catch (e) {
      console.error('存储失败:', e);
      return false;
    }
  },
  
  // 获取存储
  get(key, defaultValue = null) {
    try {
      const value = wx.getStorageSync(key);
      return value !== '' ? value : defaultValue;
    } catch (e) {
      console.error('读取存储失败:', e);
      return defaultValue;
    }
  },
  
  // 删除存储
  remove(key) {
    try {
      wx.removeStorageSync(key);
      return true;
    } catch (e) {
      console.error('删除存储失败:', e);
      return false;
    }
  },
  
  // 清空存储
  clear() {
    try {
      wx.clearStorageSync();
      return true;
    } catch (e) {
      console.error('清空存储失败:', e);
      return false;
    }
  },
  
  // 获取存储信息
  getInfo() {
    try {
      return wx.getStorageInfoSync();
    } catch (e) {
      console.error('获取存储信息失败:', e);
      return null;
    }
  }
};

module.exports = storage;'''
        
        utils_dir = os.path.join(project_path, "utils")
        with open(os.path.join(utils_dir, "storage.js"), 'w', encoding='utf-8') as f:
            f.write(content)


def main():
    """主函数"""
    print("🎯 简化版微信小程序生成器")
    print("=" * 50)
    
    generator = SimpleMiniProgramGenerator()
    
    description = input("请描述您想要的微信小程序功能: ").strip()
    if not description:
        description = "待办事项管理小程序"
    
    project_name = input("请输入项目名称 (默认: miniprogram): ").strip()
    if not project_name:
        project_name = "miniprogram"
    
    success = generator.generate_project(description, project_name)
    
    if success:
        print(f"\n🎉 项目生成成功!")
        print(f"📁 项目路径: {os.path.join(generator.output_dir, project_name)}")
        print(f"🚀 可以直接用微信开发者工具打开项目文件夹!")
        
        print(f"\n📋 生成的文件包括:")
        print(f"   - app.json: 应用配置")
        print(f"   - app.js: 应用逻辑")
        print(f"   - app.wxss: 全局样式")
        print(f"   - pages/index/: 首页")
        print(f"   - pages/todo/: 待办页面")
        print(f"   - utils/storage.js: 本地存储工具")
    else:
        print(f"\n❌ 项目生成失败")


if __name__ == "__main__":
    main()
