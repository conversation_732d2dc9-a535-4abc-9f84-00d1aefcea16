# -*- coding: utf-8 -*-
"""
配置文件 - 微信小程序自动生成系统
"""

import os

# API配置
API_CONFIGS = {
    "qwen": {
        "base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1",
        "api_key": os.getenv("QWEN_API_KEY", "your-qwen-api-key"),
        "model": "qwen-plus"
    },
    "deepseek": {
        "base_url": "https://api.deepseek.com/v1",
        "api_key": os.getenv("DEEPSEEK_API_KEY", "your-deepseek-api-key"),
        "model": "deepseek-coder"
    },
    "kimi": {
        "base_url": "https://api.moonshot.cn/v1",
        "api_key": os.getenv("KIMI_API_KEY", "your-kimi-api-key"),
        "model": "moonshot-v1-8k"
    }
}

# 项目配置
PROJECT_CONFIG = {
    "output_dir": "project",
    "template_dir": "templates",
    "max_retries": 3,
    "timeout": 30
}

# 微信小程序基础结构
MINIPROGRAM_STRUCTURE = {
    "app.json": "应用配置文件",
    "app.js": "应用逻辑文件",
    "app.wxss": "应用样式文件",
    "pages": {
        "index": ["index.js", "index.wxml", "index.wxss", "index.json"],
        "todo": ["index.js", "index.wxml", "index.wxss", "index.json"]
    },
    "utils": ["storage.js", "request.js"],
    "components": [],
    "images": []
}

# 代码生成提示模板
CODE_GENERATION_PROMPTS = {
    "page_generation": """请为以下功能生成微信小程序的页面代码（包括 index.wxml, index.wxss, index.js, index.json）：

功能描述：
{description}

请以代码块形式输出，并说明每个文件的作用。

要求：
1. 代码要完整可运行
2. 遵循微信小程序开发规范
3. 包含必要的错误处理
4. 界面美观实用
5. 代码注释清晰

输出格式：
```javascript
// index.js
// 文件作用：页面逻辑代码
[代码内容]
```

```xml
<!-- index.wxml -->
<!-- 文件作用：页面结构 -->
[代码内容]
```

```css
/* index.wxss */
/* 文件作用：页面样式 */
[代码内容]
```

```json
// index.json
// 文件作用：页面配置
[代码内容]
```""",

    "utils_generation": """请生成微信小程序的工具类代码：

工具类型：{util_type}
功能描述：{description}

要求：
1. 代码要完整可用
2. 包含错误处理
3. 提供清晰的API接口
4. 添加详细注释

请以代码块形式输出。""",

    "app_config_generation": """请生成微信小程序的app.json配置文件：

页面列表：{pages}
功能描述：{description}

要求：
1. 包含所有页面路径
2. 设置合适的窗口配置
3. 配置底部导航栏（如需要）
4. 设置网络超时时间

请以JSON格式输出。"""
}
