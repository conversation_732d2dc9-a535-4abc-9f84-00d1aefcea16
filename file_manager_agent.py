# -*- coding: utf-8 -*-
"""
FileManager Agent - 文件管理器
使用kimi大模型进行文件组织和管理
"""

import os
import json
import time
import requests
from typing import Dict, List, Any
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from config import API_CONFIGS, PROJECT_CONFIG, MINIPROGRAM_STRUCTURE


class FileManagerAgent:
    """文件管理器Agent"""
    
    def __init__(self):
        self.config = API_CONFIGS["kimi"]
        self.project_config = PROJECT_CONFIG
        self.structure = MINIPROGRAM_STRUCTURE
        self.session = self._create_session()

    def _create_session(self):
        """创建带有重试机制的会话"""
        session = requests.Session()

        retry_strategy = Retry(
            total=PROJECT_CONFIG["max_retries"],
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["POST"]
        )

        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)

        return session
        
    def _call_kimi_api(self, prompt: str) -> str:
        """调用kimi API，带重试机制"""
        headers = {
            "Authorization": f"Bearer {self.config['api_key']}",
            "Content-Type": "application/json"
        }

        data = {
            "model": self.config["model"],
            "messages": [
                {"role": "system", "content": "你是一个专业的文件系统管理专家，负责创建和组织微信小程序项目结构。"},
                {"role": "user", "content": prompt}
            ],
            "temperature": 0.1,
            "max_tokens": 2000
        }

        for attempt in range(self.project_config["max_retries"]):
            try:
                response = self.session.post(
                    f"{self.config['base_url']}/chat/completions",
                    headers=headers,
                    json=data,
                    timeout=(self.project_config["connect_timeout"], self.project_config["read_timeout"])
                )
                response.raise_for_status()
                result = response.json()
                return result["choices"][0]["message"]["content"]

            except requests.exceptions.Timeout as e:
                print(f"   ⚠️  Kimi API超时 (尝试 {attempt + 1}/{self.project_config['max_retries']})")
                if attempt < self.project_config["max_retries"] - 1:
                    time.sleep(self.project_config["retry_delay"])
                    continue

            except Exception as e:
                print(f"   ⚠️  Kimi API调用失败 (尝试 {attempt + 1}/{self.project_config['max_retries']}): {e}")
                if attempt < self.project_config["max_retries"] - 1:
                    time.sleep(self.project_config["retry_delay"])
                    continue

        print(f"   ❌ Kimi API调用失败，所有重试已用完")
        return ""
    
    def create_project_structure(self, project_name: str, requirements: Dict[str, Any]) -> bool:
        """创建项目结构"""
        try:
            project_path = os.path.join(self.project_config["output_dir"], project_name)
            
            # 创建根目录
            os.makedirs(project_path, exist_ok=True)
            
            # 创建pages目录
            pages_dir = os.path.join(project_path, "pages")
            os.makedirs(pages_dir, exist_ok=True)
            
            # 为每个页面创建目录
            for page in requirements.get("pages", []):
                page_name = page.get("name", "index")
                page_dir = os.path.join(pages_dir, page_name)
                os.makedirs(page_dir, exist_ok=True)
            
            # 创建utils目录
            utils_dir = os.path.join(project_path, "utils")
            os.makedirs(utils_dir, exist_ok=True)
            
            # 创建components目录
            components_dir = os.path.join(project_path, "components")
            os.makedirs(components_dir, exist_ok=True)
            
            # 创建images目录
            images_dir = os.path.join(project_path, "images")
            os.makedirs(images_dir, exist_ok=True)
            
            print(f"项目结构创建成功: {project_path}")
            return True
            
        except Exception as e:
            print(f"创建项目结构失败: {e}")
            return False
    
    def save_file(self, file_path: str, content: str) -> bool:
        """保存文件"""
        try:
            # 确保目录存在
            dir_path = os.path.dirname(file_path)
            if dir_path:
                os.makedirs(dir_path, exist_ok=True)
            
            # 写入文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"文件保存成功: {file_path}")
            return True
            
        except Exception as e:
            print(f"保存文件失败 {file_path}: {e}")
            return False
    
    def save_page_files(self, project_name: str, page_name: str, code_files: Dict[str, str]) -> bool:
        """保存页面文件"""
        try:
            project_path = os.path.join(self.project_config["output_dir"], project_name)
            page_dir = os.path.join(project_path, "pages", page_name)
            
            for filename, content in code_files.items():
                file_path = os.path.join(page_dir, filename)
                if not self.save_file(file_path, content):
                    return False
            
            return True
            
        except Exception as e:
            print(f"保存页面文件失败: {e}")
            return False
    
    def save_util_file(self, project_name: str, util_name: str, content: str) -> bool:
        """保存工具文件"""
        try:
            project_path = os.path.join(self.project_config["output_dir"], project_name)
            utils_dir = os.path.join(project_path, "utils")
            file_path = os.path.join(utils_dir, f"{util_name}.js")
            
            return self.save_file(file_path, content)
            
        except Exception as e:
            print(f"保存工具文件失败: {e}")
            return False
    
    def save_app_files(self, project_name: str, app_config: str, requirements: Dict[str, Any]) -> bool:
        """保存应用文件"""
        try:
            project_path = os.path.join(self.project_config["output_dir"], project_name)
            
            # 保存app.json
            app_json_path = os.path.join(project_path, "app.json")
            if not self.save_file(app_json_path, app_config):
                return False
            
            # 生成并保存app.js
            app_js_content = self._generate_app_js(requirements)
            app_js_path = os.path.join(project_path, "app.js")
            if not self.save_file(app_js_path, app_js_content):
                return False
            
            # 生成并保存app.wxss
            app_wxss_content = self._generate_app_wxss()
            app_wxss_path = os.path.join(project_path, "app.wxss")
            if not self.save_file(app_wxss_path, app_wxss_content):
                return False
            
            return True
            
        except Exception as e:
            print(f"保存应用文件失败: {e}")
            return False
    
    def _generate_app_js(self, requirements: Dict[str, Any]) -> str:
        """生成app.js内容"""
        return f'''// app.js
// 应用程序入口文件
App({{
  onLaunch: function () {{
    console.log('小程序启动');
    
    // 获取用户信息
    if (wx.getUserProfile) {{
      this.globalData.canIUseGetUserProfile = true;
    }}
  }},
  
  onShow: function () {{
    console.log('小程序显示');
  }},
  
  onHide: function () {{
    console.log('小程序隐藏');
  }},
  
  onError: function (msg) {{
    console.error('小程序错误:', msg);
  }},
  
  globalData: {{
    userInfo: null,
    canIUseGetUserProfile: false,
    appName: '{requirements.get("project_name", "微信小程序")}',
    version: '1.0.0'
  }}
}});'''
    
    def _generate_app_wxss(self) -> str:
        """生成app.wxss内容"""
        return '''/* app.wxss */
/* 全局样式 */
page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
}

.container {
  padding: 20rpx;
  min-height: 100vh;
}

/* 通用按钮样式 */
.btn {
  background-color: #07c160;
  color: white;
  border-radius: 8rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  border: none;
  margin: 10rpx 0;
}

.btn:active {
  background-color: #06ad56;
}

.btn-secondary {
  background-color: #f0f0f0;
  color: #333;
}

.btn-secondary:active {
  background-color: #e0e0e0;
}

/* 通用文本样式 */
.text-primary {
  color: #07c160;
}

.text-secondary {
  color: #888;
}

.text-center {
  text-align: center;
}

/* 通用布局样式 */
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-center {
  justify-content: center;
  align-items: center;
}

.flex-between {
  justify-content: space-between;
}

/* 通用间距样式 */
.mt-10 { margin-top: 20rpx; }
.mt-20 { margin-top: 40rpx; }
.mb-10 { margin-bottom: 20rpx; }
.mb-20 { margin-bottom: 40rpx; }
.ml-10 { margin-left: 20rpx; }
.mr-10 { margin-right: 20rpx; }

.pt-10 { padding-top: 20rpx; }
.pt-20 { padding-top: 40rpx; }
.pb-10 { padding-bottom: 20rpx; }
.pb-20 { padding-bottom: 40rpx; }
.pl-10 { padding-left: 20rpx; }
.pr-10 { padding-right: 20rpx; }'''
    
    def get_project_files(self, project_name: str) -> List[str]:
        """获取项目文件列表"""
        try:
            project_path = os.path.join(self.project_config["output_dir"], project_name)
            files = []
            
            for root, dirs, filenames in os.walk(project_path):
                for filename in filenames:
                    file_path = os.path.join(root, filename)
                    relative_path = os.path.relpath(file_path, project_path)
                    files.append(relative_path)
            
            return files
            
        except Exception as e:
            print(f"获取项目文件列表失败: {e}")
            return []
    
    def optimize_project_structure(self, project_name: str) -> Dict[str, Any]:
        """优化项目结构"""
        prompt = f"""
        请分析微信小程序项目结构并提供优化建议：

        项目名称：{project_name}
        项目路径：{os.path.join(self.project_config["output_dir"], project_name)}

        请检查：
        1. 文件组织是否合理
        2. 是否缺少必要的文件
        3. 是否有冗余文件
        4. 目录结构是否符合微信小程序规范

        请以JSON格式输出优化建议。
        """
        
        response = self._call_kimi_api(prompt)
        
        try:
            import re
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                return json.loads(json_match.group())
        except:
            pass
        
        return {
            "suggestions": ["项目结构基本合理"],
            "missing_files": [],
            "redundant_files": [],
            "optimizations": []
        }
