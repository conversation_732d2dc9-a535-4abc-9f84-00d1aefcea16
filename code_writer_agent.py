# -*- coding: utf-8 -*-
"""
CodeWriter Agent - 代码生成器
使用deepseek大模型生成微信小程序代码
"""

import re
import requests
import time
from typing import Dict, List, Tuple
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from config import API_CONFIGS, CODE_GENERATION_PROMPTS, PROJECT_CONFIG


class CodeWriterAgent:
    """代码生成器Agent"""
    
    def __init__(self):
        self.config = API_CONFIGS["deepseek"]
        self.prompts = CODE_GENERATION_PROMPTS
        self.session = self._create_session()

    def _create_session(self):
        """创建带有重试机制的会话"""
        session = requests.Session()

        # 配置重试策略
        retry_strategy = Retry(
            total=PROJECT_CONFIG["max_retries"],
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["POST"]
        )

        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)

        return session

    def _call_deepseek_api(self, prompt: str) -> str:
        """调用deepseek API，带重试机制"""
        headers = {
            "Authorization": f"Bearer {self.config['api_key']}",
            "Content-Type": "application/json"
        }

        data = {
            "model": self.config["model"],
            "messages": [
                {"role": "system", "content": "你是一个专业的微信小程序开发工程师，精通WXML、WXSS、JavaScript和微信小程序API。请生成高质量、可运行的代码。"},
                {"role": "user", "content": prompt}
            ],
            "temperature": 0.3,
            "max_tokens": 4000
        }

        for attempt in range(PROJECT_CONFIG["max_retries"]):
            try:
                print(f"   正在调用DeepSeek API (尝试 {attempt + 1}/{PROJECT_CONFIG['max_retries']})...")

                response = self.session.post(
                    f"{self.config['base_url']}/chat/completions",
                    headers=headers,
                    json=data,
                    timeout=(PROJECT_CONFIG["connect_timeout"], PROJECT_CONFIG["read_timeout"])
                )
                response.raise_for_status()
                result = response.json()

                print(f"   ✅ DeepSeek API调用成功")
                return result["choices"][0]["message"]["content"]

            except requests.exceptions.Timeout as e:
                print(f"   ⚠️  DeepSeek API超时 (尝试 {attempt + 1}/{PROJECT_CONFIG['max_retries']}): {e}")
                if attempt < PROJECT_CONFIG["max_retries"] - 1:
                    time.sleep(PROJECT_CONFIG["retry_delay"])
                    continue

            except requests.exceptions.RequestException as e:
                print(f"   ⚠️  DeepSeek API请求失败 (尝试 {attempt + 1}/{PROJECT_CONFIG['max_retries']}): {e}")
                if attempt < PROJECT_CONFIG["max_retries"] - 1:
                    time.sleep(PROJECT_CONFIG["retry_delay"])
                    continue

            except Exception as e:
                print(f"   ❌ DeepSeek API调用异常 (尝试 {attempt + 1}/{PROJECT_CONFIG['max_retries']}): {e}")
                if attempt < PROJECT_CONFIG["max_retries"] - 1:
                    time.sleep(PROJECT_CONFIG["retry_delay"])
                    continue

        print(f"   ❌ DeepSeek API调用失败，所有重试已用完，使用默认代码")
        return ""
    
    def generate_page_code(self, page_name: str, description: str) -> Dict[str, str]:
        """生成页面代码"""
        prompt = self.prompts["page_generation"].format(description=description)
        response = self._call_deepseek_api(prompt)
        
        # 解析代码块
        code_files = {}
        
        # 提取JavaScript代码
        js_pattern = r'```(?:javascript|js)\s*\n(.*?)\n```'
        js_matches = re.findall(js_pattern, response, re.DOTALL)
        if js_matches:
            code_files[f"{page_name}.js"] = js_matches[0].strip()
        
        # 提取WXML代码
        wxml_pattern = r'```(?:xml|wxml)\s*\n(.*?)\n```'
        wxml_matches = re.findall(wxml_pattern, response, re.DOTALL)
        if wxml_matches:
            code_files[f"{page_name}.wxml"] = wxml_matches[0].strip()
        
        # 提取WXSS代码
        wxss_pattern = r'```(?:css|wxss)\s*\n(.*?)\n```'
        wxss_matches = re.findall(wxss_pattern, response, re.DOTALL)
        if wxss_matches:
            code_files[f"{page_name}.wxss"] = wxss_matches[0].strip()
        
        # 提取JSON代码
        json_pattern = r'```(?:json)\s*\n(.*?)\n```'
        json_matches = re.findall(json_pattern, response, re.DOTALL)
        if json_matches:
            code_files[f"{page_name}.json"] = json_matches[0].strip()
        
        # 如果没有提取到代码，生成默认代码
        if not code_files:
            code_files = self._generate_default_page_code(page_name, description)
        
        return code_files
    
    def _generate_default_page_code(self, page_name: str, description: str) -> Dict[str, str]:
        """生成默认页面代码"""
        return {
            f"{page_name}.js": f'''// {page_name}.js
// 页面逻辑代码
Page({{
  data: {{
    title: '{description}'
  }},
  
  onLoad: function(options) {{
    console.log('页面加载完成');
  }},
  
  onShow: function() {{
    console.log('页面显示');
  }}
}});''',
            
            f"{page_name}.wxml": f'''<!-- {page_name}.wxml -->
<!-- 页面结构 -->
<view class="container">
  <view class="title">{{{{title}}}}</view>
  <view class="content">
    <text>欢迎使用微信小程序</text>
  </view>
</view>''',
            
            f"{page_name}.wxss": f'''/* {page_name}.wxss */
/* 页面样式 */
.container {{
  padding: 20rpx;
  min-height: 100vh;
  background-color: #f5f5f5;
}}

.title {{
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 40rpx;
  color: #333;
}}

.content {{
  background-color: #fff;
  padding: 30rpx;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}}''',
            
            f"{page_name}.json": '''{
  "navigationBarTitleText": "页面标题",
  "enablePullDownRefresh": false
}'''
        }
    
    def generate_util_code(self, util_name: str, description: str) -> str:
        """生成工具类代码"""
        prompt = self.prompts["utils_generation"].format(
            util_type=util_name,
            description=description
        )
        
        response = self._call_deepseek_api(prompt)
        
        # 提取代码块
        code_pattern = r'```(?:javascript|js)\s*\n(.*?)\n```'
        matches = re.findall(code_pattern, response, re.DOTALL)
        
        if matches:
            return matches[0].strip()
        
        # 默认工具代码
        if util_name == "storage":
            return '''// storage.js
// 本地存储工具类
const storage = {
  // 设置存储
  set(key, value) {
    try {
      wx.setStorageSync(key, value);
      return true;
    } catch (e) {
      console.error('存储失败:', e);
      return false;
    }
  },
  
  // 获取存储
  get(key, defaultValue = null) {
    try {
      return wx.getStorageSync(key) || defaultValue;
    } catch (e) {
      console.error('读取存储失败:', e);
      return defaultValue;
    }
  },
  
  // 删除存储
  remove(key) {
    try {
      wx.removeStorageSync(key);
      return true;
    } catch (e) {
      console.error('删除存储失败:', e);
      return false;
    }
  },
  
  // 清空存储
  clear() {
    try {
      wx.clearStorageSync();
      return true;
    } catch (e) {
      console.error('清空存储失败:', e);
      return false;
    }
  }
};

module.exports = storage;'''
        
        return f'''// {util_name}.js
// {description}
const {util_name} = {{
  // 在这里添加具体的功能实现
}};

module.exports = {util_name};'''
    
    def generate_app_config(self, pages: List[str], description: str) -> str:
        """生成app.json配置"""
        prompt = self.prompts["app_config_generation"].format(
            pages=pages,
            description=description
        )
        
        response = self._call_deepseek_api(prompt)
        
        # 提取JSON代码
        json_pattern = r'```(?:json)\s*\n(.*?)\n```'
        matches = re.findall(json_pattern, response, re.DOTALL)
        
        if matches:
            return matches[0].strip()
        
        # 默认配置
        return f'''{{
  "pages": {pages},
  "window": {{
    "backgroundTextStyle": "light",
    "navigationBarBackgroundColor": "#fff",
    "navigationBarTitleText": "微信小程序",
    "navigationBarTextStyle": "black"
  }},
  "networkTimeout": {{
    "request": 10000,
    "downloadFile": 10000
  }},
  "debug": true
}}'''
